2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] WARN  o.s.w.s.h.HandlerMappingIntrospector - Cache miss for REQUEST dispatch to '/api/auth/login' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/auth/login
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: public
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.AuthTenantFilter - AuthTenantFilter processing auth request: /api/auth/login
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - TenantFilter skipping auth endpoint: /api/auth/login
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.cms.config.MediaUploadCorsFilter - MediaUploadCorsFilter processing request: POST /api/auth/login from origin: http://localhost:3001
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.config.TenantContextInterceptor - TenantContextInterceptor - Request: POST /api/auth/login - Current tenant: public
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	jdk.internal.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:519)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createEntityManager(Unknown Source)
	org.springframework.orm.jpa.EntityManagerFactoryAccessor.createEntityManager(EntityManagerFactoryAccessor.java:169)
	org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor.preHandle(OpenEntityManagerInViewInterceptor.java:88)
	org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter.preHandle(WebRequestHandlerInterceptorAdapter.java:57)
	org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.MediaUploadCorsFilter.doFilter(MediaUploadCorsFilter.java:90)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.TenantFilter.doFilter(TenantFilter.java:32)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.AuthTenantFilter.doFilter(AuthTenantFilter.java:40)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.ApiTokenAuthenticationFilter.doFilterInternal(ApiTokenAuthenticationFilter.java:79)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:90)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.controller.AuthController.authenticateUser() with argument[s] = [LoginRequest(username=Pranav@redberyltech_com, password=Pranav@123)]
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Login attempt for username: Pranav@redberyltech_com
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Clearing tenant context, current value: null, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Tenant context cleared before login attempt
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Current tenant context before authentication: public
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.s.TenantAwareAuthenticationProvider - TenantAwareAuthenticationProvider authenticating: Pranav@redberyltech_com
2025-05-27 11:28:22 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.s.TenantAwareAuthenticationProvider - Tenant extracted from username: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: "$user", public
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: public
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: public
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: public
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(612545710485400))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@6a926065
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(612545710485400).schemaName) 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(612545710485400)]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).description]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).name]
 |  \-BasicFetch [com.cms.entity.Tenant(612545710485400).schemaName]

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(612545710485400)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(612545710485400) -> EntityResultInitializer(com.cms.entity.Tenant(612545710485400))@1777694664 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(612545710485400)): 100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(612545710485400)#100] : 1911604602
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-27T11:08:27.668569]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Auto-created tenant for user Pranav]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-27T11:08:27.668569]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [redberyltech_com]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [redberyltech_com]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(612545710485400)#100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@4eaf9bcd
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.util.TenantUtils - Setting tenant context to: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.query.plan.cache - Creating and caching NativeQuery ParameterInterpretation - ParameterInterpretationImpl (SELECT current_schema()) : {
}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@2c210d5a
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Current database schema: public, expected: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: redberyltech_com, Actual: public
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Attempting to force schema change to: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.query.plan.cache - Creating and caching NativeQuery ParameterInterpretation - ParameterInterpretationImpl (SET search_path TO redberyltech_com) : {
}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@671a11c5
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [redberyltech_com]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Schema after forced change: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(612546216834400))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@1488ec93
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(612546216834400).username) 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(612546216834400)]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).email]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isActive]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isLoggedIn]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).password]
 |  \-BasicFetch [com.cms.entity.User(612546216834400).username]

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(612546216834400)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(612546216834400) -> EntityResultInitializer(com.cms.entity.User(612546216834400))@676327176 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.is_logged_in,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.User(612546216834400)): 100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.User(612546216834400)#100] : 1717534377
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-27T11:08:27.859730]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [<EMAIL>]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [true]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [2025-05-27T11:24:49.080223]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [Pranav@redberyltech_com]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [$2a$10$WBy34Vzda.P2v4Mk9o0L5.7H.CQ/Gg64hC4nu3tSOo4CCYcSzluPm]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [9] - [Pranav]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.User(612546216834400)#100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@313feac5
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - User found: Pranav, ID: 100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional[User(id=100, username=Pranav, email=<EMAIL>, password=$2a$10$WBy34Vzda.P2v4Mk9o0L5.7H.CQ/Gg64hC4nu3tSOo4CCYcSzluPm, isActive=true, isLoggedIn=true)]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - User found: Pranav, ID: 100, Active: true, LoggedIn: true
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-27T11:08:27.859730, password=$2a$10$WBy34Vzda.P2v4Mk9o0L5.7H.CQ/Gg64hC4nu3tSOo4CCYcSzluPm, createdBy=anonymousUser, modifiedAt=2025-05-27T11:24:49.080223, isLoggedIn=true, modifiedBy=Pranav@redberyltech_com, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.s.TenantAwareAuthenticationProvider - Performing additional authentication checks for user: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.s.TenantAwareAuthenticationProvider - Password verification successful for user: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.s.TenantAwareAuthenticationProvider - Authenticated user
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.s.TenantAwareAuthenticationProvider - Authentication successful for user: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Tenant context after authentication: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.security.JwtTokenProvider - Generating token for user: Pranav@redberyltech_com with tenant: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.security.JwtTokenProvider - Added tenant to JWT claims: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.updateLoginStatus() with argument[s] = [Pranav@redberyltech_com, true]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Updating login status for user 'Pranav@redberyltech_com' in tenant 'redberyltech_com' to: true
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Looking for user 'Pranav' in tenant 'redberyltech_com'
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(612546216834400))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@4c144016
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(612546216834400).username) 
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(612546216834400)]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).email]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isActive]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isLoggedIn]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).password]
 |  \-BasicFetch [com.cms.entity.User(612546216834400).username]

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(612546216834400)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-27T11:08:27.859730, password=$2a$10$WBy34Vzda.P2v4Mk9o0L5.7H.CQ/Gg64hC4nu3tSOo4CCYcSzluPm, createdBy=anonymousUser, modifiedAt=2025-05-27T11:24:49.080223, isLoggedIn=true, modifiedBy=Pranav@redberyltech_com, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(612546216834400) -> EntityResultInitializer(com.cms.entity.User(612546216834400))@152885653 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.is_logged_in,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.User(612546216834400)): 100
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@102f6ff4
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.j.i.PersistenceUnitUtilImpl - jakarta.persistence.PersistenceUnitUtil.getIdentifier is only intended to work with enhanced entities (although Hibernate also adapts this support to its proxies); however the passed entity was not enhanced (nor a proxy).. may not be able to read identifier
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Login status updated successfully for user 'Pranav' in tenant 'redberyltech_com': true -> true
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.updateLoginStatus() with result = null
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-27T11:08:27.668569, createdBy=anonymousUser, modifiedAt=2025-05-27T11:08:27.668569, name=redberyltech_com, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=redberyltech_com}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-27T11:08:27.859730, password=$2a$10$WBy34Vzda.P2v4Mk9o0L5.7H.CQ/Gg64hC4nu3tSOo4CCYcSzluPm, createdBy=anonymousUser, modifiedAt=2025-05-27T11:24:49.080223, isLoggedIn=true, modifiedBy=Pranav@redberyltech_com, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Login status updated to true for user: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Login successful for user: Pranav@redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Login process completed, tenant context preserved: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.controller.AuthController.authenticateUser() with result = <200 OK OK,JwtAuthResponse(accessToken=eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnQiOiJyZWRiZXJ5bHRlY2hfY29tIiwic3ViIjoiUHJhbmF2QHJlZGJlcnlsdGVjaF9jb20iLCJpYXQiOjE3NDgzMjU1MDMsImV4cCI6MTc0ODQxMTkwM30.IMf-qw_wTgH23ZSsdASKwbIKoTafUCk5yC-FjUBQrqt1y-54JS-HXa7wPYKnEJDZIDfHkN_hh4dRihhKBZU-vQ, tokenType=Bearer),[]>
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - Post-handle for request: /api/auth/login - Current tenant: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - After completion for request: /api/auth/login - Final tenant: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/collections/getAll
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.JwtAuthenticationFilter - Setting tenant from header: redberyltech_com
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:23 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Tenant context after setting from header: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Username from token: Pranav@redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:584)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createNativeEntityManager(Unknown Source)
	org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:484)
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:409)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(612545710485400))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@2df7631c
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(612545710485400).schemaName) 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(612545710485400)]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).description]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).name]
 |  \-BasicFetch [com.cms.entity.Tenant(612545710485400).schemaName]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(612545710485400)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(612545710485400) -> EntityResultInitializer(com.cms.entity.Tenant(612545710485400))@2121684770 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.util.TenantUtils - Tenant not found with exact match, trying case-insensitive search
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(612564768000300))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(612564768000300)]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).description]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612564768000300).name]
 |  \-BasicFetch [com.cms.entity.Tenant(612564768000300).schemaName]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(612564768000300)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(612564768000300) -> EntityResultInitializer(com.cms.entity.Tenant(612564768000300))@879056473 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [1]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(612564768000300)): 1
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(612564768000300)#1] : 1047911248
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Default public tenant]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [Public Tenant]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [public]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(612564768000300)#1
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@20bc8559
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] WARN  com.cms.util.TenantUtils - Tenant not found or inactive: redberyltech_com, using default tenant
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@35f0ba15
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [redberyltech_com]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Current database schema: redberyltech_com, expected: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: public, Actual: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Attempting to force schema change to: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.query.plan.cache - Creating and caching NativeQuery ParameterInterpretation - ParameterInterpretationImpl (SET search_path TO public) : {
}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@11f2190a
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Schema after forced change: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(612546216834400))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@4094f468
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(612546216834400).username) 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(612546216834400)]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).email]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isActive]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isLoggedIn]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).password]
 |  \-BasicFetch [com.cms.entity.User(612546216834400).username]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(612546216834400)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(612546216834400) -> EntityResultInitializer(com.cms.entity.User(612546216834400))@1872284921 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.is_logged_in,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - No user found with username 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional.empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] WARN  c.c.security.UserDetailsServiceImpl - User not found with username: Pranav in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] ERROR c.c.security.UserDetailsServiceImpl - Error during user authentication for: Pranav@redberyltech_com
org.springframework.security.core.userdetails.UsernameNotFoundException: User not found with username: Pranav in tenant: public
	at com.cms.security.UserDetailsServiceImpl.lambda$0(UserDetailsServiceImpl.java:41)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.cms.security.UserDetailsServiceImpl.loadUserByUsername(UserDetailsServiceImpl.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	at com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - rolling back
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] WARN  c.c.security.JwtAuthenticationFilter - Could not set user authentication: User not found with username: Pranav in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - TenantFilter processing request: /api/collections/getAll, Auth header present: true
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - X-TenantID header value: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - Current tenant context before processing: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantFilter - Setting tenant context from header: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.cms.config.MediaUploadCorsFilter - MediaUploadCorsFilter processing request: GET /api/collections/getAll from origin: http://localhost:3001
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.config.TenantContextInterceptor - TenantContextInterceptor - Request: GET /api/collections/getAll - Current tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	jdk.internal.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:519)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createEntityManager(Unknown Source)
	org.springframework.orm.jpa.EntityManagerFactoryAccessor.createEntityManager(EntityManagerFactoryAccessor.java:169)
	org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor.preHandle(OpenEntityManagerInViewInterceptor.java:88)
	org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter.preHandle(WebRequestHandlerInterceptorAdapter.java:57)
	org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.MediaUploadCorsFilter.doFilter(MediaUploadCorsFilter.java:90)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.TenantFilter.doFilter(TenantFilter.java:59)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.AuthTenantFilter.doFilter(AuthTenantFilter.java:45)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.ApiTokenAuthenticationFilter.doFilterInternal(ApiTokenAuthenticationFilter.java:79)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:90)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.controller.CollectionListingController.getAllCollections() with argument[s] = []
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.impl.CollectionListingServiceImpl.getAllCollections() with argument[s] = []
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : cl1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.CollectionListing(612564848328100))] with identifierForTableGroup [com.cms.entity.CollectionListing] for NavigablePath [com.cms.entity.CollectionListing] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [org.hibernate.sql.ast.tree.from.LazyTableGroup@27ca8c47] with identifierForTableGroup [com.cms.entity.CollectionListing.category] for NavigablePath [com.cms.entity.CollectionListing.category] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.CollectionListing(612564848328100)]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).additionalInformation]
 |  +-EntityFetchSelectImpl [com.cms.entity.CollectionListing(612564848328100).category]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).collectionApiId]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).collectionDesc]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).collectionName]
 |  +-SelectEagerCollectionFetch [com.cms.entity.CollectionListing(612564848328100).components]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).createdAt]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).createdBy]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).disclaimerText]
 |  +-DelayedCollectionFetch [com.cms.entity.CollectionListing(612564848328100).fields]
 |  +-BasicFetch [com.cms.entity.CollectionListing(612564848328100).modifiedAt]
 |  \-BasicFetch [com.cms.entity.CollectionListing(612564848328100).modifiedBy]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (cl1 : com.cms.entity.CollectionListing(612564848328100)) {
          primaryTableReference : collection_listing as cl1_0
          TableGroupJoins {
            left  join LazyTableGroup (c1 : com.cms.entity.CollectionListing(612564848328100).category) {
            }
          }
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.CollectionListing(612564848328100) -> EntityResultInitializer(com.cms.entity.CollectionListing(612564848328100))@1833170927 (SingleTableEntityPersister(com.cms.entity.CollectionListing))
	  com.cms.entity.CollectionListing(612564848328100).fields -> DelayedCollectionInitializer(com.cms.entity.CollectionListing(612564848328100).fields)@2123355511 (PluralAttribute(com.cms.entity.CollectionListing.fields))
	  com.cms.entity.CollectionListing(612564848328100).components -> SelectEagerCollectionInitializer(com.cms.entity.CollectionListing(612564848328100).components)@550945196 (PluralAttribute(com.cms.entity.CollectionListing.components))
	  com.cms.entity.CollectionListing(612564848328100).category -> EntitySelectFetchInitializer(com.cms.entity.CollectionListing(612564848328100).category)@2131677813 (ToOneAttributeMapping(NavigableRole[com.cms.entity.CollectionListing.category])@218152692)

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        cl1_0.id,
        cl1_0.additional_information,
        cl1_0.category_id,
        cl1_0.collection_api_id,
        cl1_0.collection_desc,
        cl1_0.collection_name,
        cl1_0.created_at,
        cl1_0.created_by,
        cl1_0.disclaimer_text,
        cl1_0.modified_at,
        cl1_0.modified_by 
    from
        collection_listing cl1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.s.i.CollectionListingServiceImpl - Retrieved 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.impl.CollectionListingServiceImpl.getAllCollections() with result = []
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.controller.CollectionListingController.getAllCollections() with result = <200 OK OK,[],[]>
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - Post-handle for request: /api/collections/getAll - Current tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - After completion for request: /api/collections/getAll - Final tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - Preserving tenant context after authenticated request: /api/collections/getAll, tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/content-entries/getAll
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.security.JwtAuthenticationFilter - Setting tenant from header: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.security.JwtAuthenticationFilter - Tenant context after setting from header: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.security.JwtAuthenticationFilter - Username from token: Pranav@redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Getting tenant context: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:584)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createNativeEntityManager(Unknown Source)
	org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:484)
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:409)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO redberyltech_com, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(612545710485400))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@65510dc4
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(612545710485400).schemaName) 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(612545710485400)]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).description]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612545710485400).name]
 |  \-BasicFetch [com.cms.entity.Tenant(612545710485400).schemaName]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(612545710485400)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(612545710485400) -> EntityResultInitializer(com.cms.entity.Tenant(612545710485400))@277296418 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG com.cms.util.TenantUtils - Tenant not found with exact match, trying case-insensitive search
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(612564905847700))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(612564905847700)]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).description]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(612564905847700).name]
 |  \-BasicFetch [com.cms.entity.Tenant(612564905847700).schemaName]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(612564905847700)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(612564905847700) -> EntityResultInitializer(com.cms.entity.Tenant(612564905847700))@1642100418 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [1]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(612564905847700)): 1
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(612564905847700)#1] : 561536951
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Default public tenant]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [null]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [Public Tenant]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [public]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(612564905847700)#1
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@434c7b3c
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] WARN  com.cms.util.TenantUtils - Tenant not found or inactive: redberyltech_com, using default tenant
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@3cd3dd22
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [redberyltech_com]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.service.UserService - Current database schema: redberyltech_com, expected: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: public, Actual: redberyltech_com
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.service.UserService - Attempting to force schema change to: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@3c0851b9
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.service.UserService - Schema after forced change: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(612546216834400))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@674affe5
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(612546216834400).username) 
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(612546216834400)]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).createdBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).email]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isActive]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).isLoggedIn]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(612546216834400).password]
 |  \-BasicFetch [com.cms.entity.User(612546216834400).username]

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(612546216834400)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=null, createdBy=null, modifiedAt=null, name=Public Tenant, description=Default public tenant, modifiedBy=null, id=1, isActive=true, schemaName=public}
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(612546216834400) -> EntityResultInitializer(com.cms.entity.User(612546216834400))@2096385621 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.is_logged_in,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.service.UserService - No user found with username 'Pranav' in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional.empty
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] WARN  c.c.security.UserDetailsServiceImpl - User not found with username: Pranav in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] ERROR c.c.security.UserDetailsServiceImpl - Error during user authentication for: Pranav@redberyltech_com
org.springframework.security.core.userdetails.UsernameNotFoundException: User not found with username: Pranav in tenant: public
	at com.cms.security.UserDetailsServiceImpl.lambda$0(UserDetailsServiceImpl.java:41)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.cms.security.UserDetailsServiceImpl.loadUserByUsername(UserDetailsServiceImpl.java:39)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	at com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:833)
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG o.h.e.t.internal.TransactionImpl - rolling back
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] WARN  c.c.security.JwtAuthenticationFilter - Could not set user authentication: User not found with username: Pranav in tenant: public
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-3
2025-05-27 11:28:24 [http-nio-0.0.0.0-8071-exec-3] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: public
