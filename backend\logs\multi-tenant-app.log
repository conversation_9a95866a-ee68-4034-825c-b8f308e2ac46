2025-05-27 11:53:09 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Or [Mvc [pattern='/media/**'], Mvc [pattern='/api/media/**']] with [org.springframework.security.web.session.DisableEncodeUrlFilter@14ce2b1f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1809ab77, org.springframework.security.web.context.SecurityContextHolderFilter@196845dd, org.springframework.security.web.header.HeaderWriterFilter@67ba6388, org.springframework.web.filter.CorsFilter@6421d9dc, org.springframework.security.web.authentication.logout.LogoutFilter@63d9db68, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@42c6563f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e4327cc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1fed0303, org.springframework.security.web.session.SessionManagementFilter@5357d18d, org.springframework.security.web.access.ExceptionTranslationFilter@24412479, org.springframework.security.web.access.intercept.AuthorizationFilter@18829928]
2025-05-27 11:53:09 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5d102129, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@10c45844, org.springframework.security.web.context.SecurityContextHolderFilter@65bd587b, org.springframework.security.web.header.HeaderWriterFilter@1fafa1d6, org.springframework.web.filter.CorsFilter@40503088, org.springframework.security.web.authentication.logout.LogoutFilter@6f444ba4, com.cms.security.JwtAuthenticationFilter@274783f8, com.cms.security.ApiTokenAuthenticationFilter@56d5a50f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c779900, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e342249, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@448d0f51, org.springframework.security.web.session.SessionManagementFilter@65076b15, org.springframework.security.web.access.ExceptionTranslationFilter@3c80be62, org.springframework.security.web.access.intercept.AuthorizationFilter@10223d15]
2025-05-27 11:53:09 [main] DEBUG o.h.v.m.ResourceBundleMessageInterpolator - Loaded expression factory via original TCCL
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.AbstractConfigurationImpl - Setting custom MessageInterpolator of type org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.AbstractConfigurationImpl - Setting custom ConstraintValidatorFactory of type org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.x.config.ValidationXmlParser - Trying to load META-INF/validation.xml for XML based Validator configuration.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.x.c.ResourceLoaderHelper - Trying to load META-INF/validation.xml via user class loader
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.x.c.ResourceLoaderHelper - Trying to load META-INF/validation.xml via TCCL
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.x.c.ResourceLoaderHelper - Trying to load META-INF/validation.xml via Hibernate Validator's class loader
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.x.config.ValidationXmlParser - No META-INF/validation.xml found. Using annotation based configuration only.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.r.TraversableResolvers - Found jakarta.persistence.Persistence on classpath containing 'getPersistenceUtil'. Assuming JPA 2 environment. Trying to instantiate JPA aware TraversableResolver
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.r.TraversableResolvers - Instantiated JPA aware TraversableResolver of type org.hibernate.validator.internal.engine.resolver.JPATraversableResolver.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000252: Using org.hibernate.validator.internal.engine.DefaultPropertyNodeNameProvider as property node name provider.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000234: Using org.springframework.validation.beanvalidation.LocaleContextMessageInterpolator as ValidatorFactory-scoped message interpolator.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.resolver.JPATraversableResolver as ValidatorFactory-scoped traversable resolver.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.util.ExecutableParameterNameProvider as ValidatorFactory-scoped parameter name provider.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.DefaultClockProvider as ValidatorFactory-scoped clock provider.
2025-05-27 11:53:09 [main] DEBUG o.h.v.i.e.ValidatorFactoryConfigurationHelper - HV000234: Using org.hibernate.validator.internal.engine.scripting.DefaultScriptEvaluatorFactory as ValidatorFactory-scoped script evaluator factory.
2025-05-27 11:53:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8071 (http) with context path '/api'
2025-05-27 11:53:10 [main] INFO  c.c.ContentManagementSystemApplication - Started ContentManagementSystemApplication in 11.287 seconds (process running for 11.602)
2025-05-27 11:53:10 [main] INFO  c.cms.util.DatabaseMigrationExecutor - Executing SQL migration script to remove unique constraints...
2025-05-27 11:53:10 [main] INFO  c.cms.util.DatabaseMigrationExecutor - Migration completed successfully.
2025-05-27 11:53:10 [main] INFO  c.c.u.TenantSchemaMigrationExecutor - Executing SQL migration script to add category_id column to all tenant schemas...
2025-05-27 11:53:10 [main] INFO  c.c.u.TenantSchemaMigrationExecutor - Migration completed successfully.
