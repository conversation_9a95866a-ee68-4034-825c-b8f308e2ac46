2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/auth/add-user-to-tenant
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.security.JwtTokenProvider - Extracted tenant from token: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.JwtAuthenticationFilter - Setting tenant from JWT token: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.security.JwtAuthenticationFilter - Tenant context after setting from token: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.security.JwtAuthenticationFilter - Username from token: Pranav@rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:584)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createNativeEntityManager(Unknown Source)
	org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:484)
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:409)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(540422419314199))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@1b15a61a
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(540422419314199).schemaName) 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(540422419314199)]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).description]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).name]
 |  \-BasicFetch [com.cms.entity.Tenant(540422419314199).schemaName]

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(540422419314199)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(540422419314199) -> EntityResultInitializer(com.cms.entity.Tenant(540422419314199))@935790543 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(540422419314199)): 100
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(540422419314199)#100] : 328449998
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.192999]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Auto-created tenant for user Pranav]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.192999]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [rcms]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [rcms]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(540422419314199)#100
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@10aa12c7
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.util.TenantUtils - Setting tenant context to: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@2bd2c4e0
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.service.UserService - Current database schema: public, expected: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: rcms, Actual: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.service.UserService - Attempting to force schema change to: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@108db0c0
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [rcms]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.service.UserService - Schema after forced change: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(540423083269200))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@37ecf2fc
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(540423083269200).username) 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(540423083269200)]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).email]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).isActive]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).password]
 |  \-BasicFetch [com.cms.entity.User(540423083269200).username]

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(540423083269200)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(540423083269200) -> EntityResultInitializer(com.cms.entity.User(540423083269200))@27299902 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.User(540423083269200)): 100
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.User(540423083269200)#100] : 527013978
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.508737]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [<EMAIL>]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.508737]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [Pranav]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.User(540423083269200)#100
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@1123869a
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.service.UserService - User found: Pranav, ID: 100
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional[User(id=100, username=Pranav, email=<EMAIL>, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, isActive=true)]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.UserDetailsServiceImpl - User found: Pranav, ID: 100, Active: true
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-26T15:26:27.508737, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.508737, modifiedBy=anonymousUser, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.security.JwtTokenProvider - Validating token - Token username: Pranav@rcms, UserDetails username: Pranav@rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.security.JwtTokenProvider - Token validation result: true
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.security.JwtAuthenticationFilter - Authentication set for user: Pranav@rcms, tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.AuthTenantFilter - AuthTenantFilter processing auth request: /api/auth/add-user-to-tenant
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.TenantFilter - TenantFilter processing request: /api/auth/add-user-to-tenant, Auth header present: true
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.TenantFilter - X-TenantID header value: null
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.TenantFilter - Current tenant context before processing: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.TenantFilter - Keeping existing tenant context: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.cms.config.MediaUploadCorsFilter - MediaUploadCorsFilter processing request: POST /api/auth/add-user-to-tenant from origin: http://localhost:3001
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.config.TenantContextInterceptor - TenantContextInterceptor - Request: POST /api/auth/add-user-to-tenant - Current tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	jdk.internal.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:519)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createEntityManager(Unknown Source)
	org.springframework.orm.jpa.EntityManagerFactoryAccessor.createEntityManager(EntityManagerFactoryAccessor.java:169)
	org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor.preHandle(OpenEntityManagerInViewInterceptor.java:88)
	org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter.preHandle(WebRequestHandlerInterceptorAdapter.java:57)
	org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.MediaUploadCorsFilter.doFilter(MediaUploadCorsFilter.java:90)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.TenantFilter.doFilter(TenantFilter.java:59)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.AuthTenantFilter.doFilter(AuthTenantFilter.java:50)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.ApiTokenAuthenticationFilter.doFilterInternal(ApiTokenAuthenticationFilter.java:40)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:90)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.controller.AuthController.addUserToTenant() with argument[s] = [AddUserToTenantRequest(username=Prathamesh, email=<EMAIL>, password=Prathamesh@123, tenantSchemaName=rcms)]
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.controller.AuthController - Adding user 'Prathamesh' to existing tenant: rcms
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Clearing tenant context, current value: rcms, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(540422447481800))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@17d86390
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(540422447481800).schemaName) 
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-BasicResult [com.cms.entity.Tenant(540422447481800).id]

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(540422447481800)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG org.hibernate.SQL - 
    select
        t1_0.id 
    from
        tenants t1_0 
    where
        t1_0.schema_name=? 
    fetch
        first ? rows only
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] WARN  com.cms.controller.AuthController - Tenant with schema name 'rcms' does not exist
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.controller.AuthController - Clearing tenant context after adding user to tenant
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - Clearing tenant context, current value: public, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.controller.AuthController.addUserToTenant() with result = <400 BAD_REQUEST Bad Request,Error: Tenant 'rcms' does not exist!,[]>
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - Post-handle for request: /api/auth/add-user-to-tenant - Current tenant: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - After completion for request: /api/auth/add-user-to-tenant - Final tenant: public
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-9
2025-05-26 15:27:50 [http-nio-0.0.0.0-8071-exec-9] DEBUG com.cms.config.TenantFilter - Preserving tenant context after authenticated request: /api/auth/add-user-to-tenant, tenant: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/auth/list-users-in-tenant
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.security.JwtTokenProvider - Extracted tenant from token: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.JwtAuthenticationFilter - Setting tenant from JWT token: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Tenant context after setting from token: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Username from token: Pranav@rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:584)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createNativeEntityManager(Unknown Source)
	org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:484)
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:409)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(540422419314199))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@3529604b
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(540422419314199).schemaName) 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(540422419314199)]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).description]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).name]
 |  \-BasicFetch [com.cms.entity.Tenant(540422419314199).schemaName]

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(540422419314199)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(540422419314199) -> EntityResultInitializer(com.cms.entity.Tenant(540422419314199))@1727893186 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(540422419314199)): 100
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(540422419314199)#100] : 339793139
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.192999]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Auto-created tenant for user Pranav]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.192999]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [rcms]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [rcms]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(540422419314199)#100
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@6259cc1f
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.util.TenantUtils - Setting tenant context to: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@5ed252c8
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Current database schema: public, expected: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: rcms, Actual: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Attempting to force schema change to: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@16426b5d
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [rcms]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - Schema after forced change: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(540423083269200))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@78d78ace
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(540423083269200).username) 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(540423083269200)]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).email]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).isActive]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).password]
 |  \-BasicFetch [com.cms.entity.User(540423083269200).username]

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(540423083269200)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(540423083269200) -> EntityResultInitializer(com.cms.entity.User(540423083269200))@2089307375 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.User(540423083269200)): 100
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.User(540423083269200)#100] : 1817221327
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.508737]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [<EMAIL>]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.508737]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [Pranav]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.User(540423083269200)#100
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@3d8ee267
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.service.UserService - User found: Pranav, ID: 100
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional[User(id=100, username=Pranav, email=<EMAIL>, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, isActive=true)]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.UserDetailsServiceImpl - User found: Pranav, ID: 100, Active: true
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-26T15:26:27.508737, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.508737, modifiedBy=anonymousUser, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.security.JwtTokenProvider - Validating token - Token username: Pranav@rcms, UserDetails username: Pranav@rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.security.JwtTokenProvider - Token validation result: true
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.security.JwtAuthenticationFilter - Authentication set for user: Pranav@rcms, tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.AuthTenantFilter - AuthTenantFilter processing auth request: /api/auth/list-users-in-tenant
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - TenantFilter processing request: /api/auth/list-users-in-tenant, Auth header present: true
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - X-TenantID header value: null
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - Current tenant context before processing: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - Keeping existing tenant context: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.cms.config.MediaUploadCorsFilter - MediaUploadCorsFilter processing request: POST /api/auth/list-users-in-tenant from origin: http://localhost:3001
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.config.TenantContextInterceptor - TenantContextInterceptor - Request: POST /api/auth/list-users-in-tenant - Current tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	jdk.internal.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:519)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createEntityManager(Unknown Source)
	org.springframework.orm.jpa.EntityManagerFactoryAccessor.createEntityManager(EntityManagerFactoryAccessor.java:169)
	org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor.preHandle(OpenEntityManagerInViewInterceptor.java:88)
	org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter.preHandle(WebRequestHandlerInterceptorAdapter.java:57)
	org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.MediaUploadCorsFilter.doFilter(MediaUploadCorsFilter.java:90)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.TenantFilter.doFilter(TenantFilter.java:59)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.AuthTenantFilter.doFilter(AuthTenantFilter.java:50)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.ApiTokenAuthenticationFilter.doFilterInternal(ApiTokenAuthenticationFilter.java:40)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:90)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.controller.AuthController.listUsersInTenant() with argument[s] = [ListUsersInTenantRequest(tenantSchemaName=rcms)]
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Listing users in tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Clearing tenant context, current value: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(540422447481800))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@40af56f2
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(540422447481800).schemaName) 
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-BasicResult [com.cms.entity.Tenant(540422447481800).id]

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(540422447481800)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG org.hibernate.SQL - 
    select
        t1_0.id 
    from
        tenants t1_0 
    where
        t1_0.schema_name=? 
    fetch
        first ? rows only
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO rcms, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: rcms
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] WARN  com.cms.controller.AuthController - Tenant with schema name 'rcms' does not exist
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.controller.AuthController - Clearing tenant context after listing users in tenant
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - Clearing tenant context, current value: public, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.controller.AuthController.listUsersInTenant() with result = <400 BAD_REQUEST Bad Request,Error: Tenant 'rcms' does not exist!,[]>
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - Post-handle for request: /api/auth/list-users-in-tenant - Current tenant: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - After completion for request: /api/auth/list-users-in-tenant - Final tenant: public
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] INFO  com.cms.config.TenantContextHolder - No tenant context found, using default tenant, thread: http-nio-0.0.0.0-8071-exec-1
2025-05-26 15:28:06 [http-nio-0.0.0.0-8071-exec-1] DEBUG com.cms.config.TenantFilter - Preserving tenant context after authenticated request: /api/auth/list-users-in-tenant, tenant: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Processing request: /api/auth/logout
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.security.JwtTokenProvider - Extracted tenant from token: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.JwtAuthenticationFilter - Setting tenant from JWT token: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Forcing tenant context to: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Tenant context after setting from token: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Username from token: Pranav@rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.createNativeEntityManager(AbstractEntityManagerFactoryBean.java:584)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:487)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createNativeEntityManager(Unknown Source)
	org.springframework.orm.jpa.JpaTransactionManager.createEntityManagerForTransaction(JpaTransactionManager.java:484)
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:409)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:531)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:405)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:610)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	com.cms.security.UserDetailsServiceImpl$$SpringCGLIB$$0.loadUserByUsername(<generated>)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:66)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - On TransactionImpl creation, JpaCompliance#isJpaTransactionComplianceEnabled == false
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - begin
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Getting connection for tenant: public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Current search_path before change: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Executing SQL: SET search_path TO public, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.MultiTenantConnectionProviderImpl - Connection schema set to: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - New search_path after change: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.MultiTenantConnectionProviderImpl - Connection established for tenant: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Loading user by username: Pranav@rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.util.TenantUtils - Attempting to find tenant with schema name: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : t1_0
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.Tenant(540422419314199))] with identifierForTableGroup [com.cms.entity.Tenant] for NavigablePath [com.cms.entity.Tenant] 
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@6bc758a6
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.Tenant(540422419314199).schemaName) 
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.Tenant(540422419314199)]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).createdBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).description]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).isActive]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedAt]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).modifiedBy]
 |  +-BasicFetch [com.cms.entity.Tenant(540422419314199).name]
 |  \-BasicFetch [com.cms.entity.Tenant(540422419314199).schemaName]

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (t1 : com.cms.entity.Tenant(540422419314199)) {
          primaryTableReference : tenants as t1_0
        }
      }
    }

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.Tenant(540422419314199) -> EntityResultInitializer(com.cms.entity.Tenant(540422419314199))@1687037926 (SingleTableEntityPersister(com.cms.entity.Tenant))

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        t1_0.id,
        t1_0.created_at,
        t1_0.created_by,
        t1_0.description,
        t1_0.is_active,
        t1_0.modified_at,
        t1_0.modified_by,
        t1_0.name,
        t1_0.schema_name 
    from
        tenants t1_0 
    where
        t1_0.schema_name=?
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.Tenant(540422419314199)): 100
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.Tenant(540422419314199)#100] : 1340130993
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.192999]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [Auto-created tenant for user Pranav]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.192999]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [rcms]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [rcms]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.Tenant(540422419314199)#100
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@2a59e215
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.util.TenantUtils - Setting tenant context to: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Setting tenant context to: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Parsed username: Pranav, tenant context set to: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - Searching for user 'Pranav' in tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.service.UserService.findByUsername() with argument[s] = [Pranav]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Finding user by username 'Pranav' in tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@364f0821
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [public]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Current database schema: public, expected: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] ERROR com.cms.service.UserService - CRITICAL: Database schema mismatch! Expected: rcms, Actual: public
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Attempting to force schema change to: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    
SET
    search_path TO rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    SELECT
        current_schema()
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list is empty
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.basic.BasicResultAssembler@635b3204
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [rcms]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - Schema after forced change: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Created new SQL alias : u1_0
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.create - Registration of TableGroup [StandardTableGroup(com.cms.entity.User(540423083269200))] with identifierForTableGroup [com.cms.entity.User] for NavigablePath [com.cms.entity.User] 
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmParameter : org.hibernate.query.sqm.tree.expression.SqmJpaCriteriaParameterWrapper@1ec7612b
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.q.s.sql.BaseSqmToSqlAstConverter - Determining mapping-model type for SqmPath : SqmBasicValuedSimplePath(com.cms.entity.User(540423083269200).username) 
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.graph.AST - DomainResult Graph:
 \-EntityResultImpl [com.cms.entity.User(540423083269200)]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).createdBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).email]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).isActive]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedAt]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).modifiedBy]
 |  +-BasicFetch [com.cms.entity.User(540423083269200).password]
 |  \-BasicFetch [com.cms.entity.User(540423083269200).username]

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.ast.tree - SQL AST Tree:
    SelectStatement {
      FromClause {
        StandardTableGroup (u1 : com.cms.entity.User(540423083269200)) {
          primaryTableReference : users as u1_0
        }
      }
    }

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 1 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.sql.exec - Skipping reading Query result cache data: cache-enabled = false, cache-mode = NORMAL
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Initializer list:
	  com.cms.entity.User(540423083269200) -> EntityResultInitializer(com.cms.entity.User(540423083269200))@912484798 (SingleTableEntityPersister(com.cms.entity.User))

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.SQL - 
    select
        u1_0.id,
        u1_0.created_at,
        u1_0.created_by,
        u1_0.email,
        u1_0.is_active,
        u1_0.modified_at,
        u1_0.modified_by,
        u1_0.password,
        u1_0.username 
    from
        users u1_0 
    where
        u1_0.username=?
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [0] - [100]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Hydrated EntityKey (com.cms.entity.User(540423083269200)): 100
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Created new entity instance [com.cms.entity.User(540423083269200)#100] : 1509118211
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [1] - [2025-05-26T15:26:27.508737]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [2] - [anonymousUser]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [3] - [<EMAIL>]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [4] - [true]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [5] - [2025-05-26T15:26:27.508737]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [6] - [anonymousUser]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [7] - [$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results - Extracted JDBC value [8] - [Pranav]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.orm.results.loading.entity - (EntityResultInitializer) Done materializing entityInstance : com.cms.entity.User(540423083269200)#100
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG org.hibernate.orm.results.loading - Calling top-level assembler (0 / 1) : org.hibernate.sql.results.graph.entity.internal.EntityAssembler@eef19d5
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.service.UserService - User found: Pranav, ID: 100
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.service.UserService.findByUsername() with result = Optional[User(id=100, username=Pranav, email=<EMAIL>, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, isActive=true)]
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.UserDetailsServiceImpl - User found: Pranav, ID: 100, Active: true
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.t.internal.TransactionImpl - committing
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Processing flush-time cascades
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Dirty checking collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 insertions, 0 updates, 0 deletions to 2 objects
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.e.i.AbstractFlushingEventListener - Flushed: 0 (re)creations, 0 updates, 0 removals to 0 collections
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - Listing entities:
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.Tenant{createdAt=2025-05-26T15:26:27.192999, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.192999, name=rcms, description=Auto-created tenant for user Pranav, modifiedBy=anonymousUser, id=100, isActive=true, schemaName=rcms}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG o.h.internal.util.EntityPrinter - com.cms.entity.User{createdAt=2025-05-26T15:26:27.508737, password=$2a$10$.D3SG9tZssEzei6SOnfmJOfphpZRZs6llfvuTJAPyCIMI.P9fQ1su, createdBy=anonymousUser, modifiedAt=2025-05-26T15:26:27.508737, modifiedBy=anonymousUser, id=100, isActive=true, email=<EMAIL>, username=Pranav}
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.security.JwtTokenProvider - Validating token - Token username: Pranav@rcms, UserDetails username: Pranav@rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.security.JwtTokenProvider - Token validation result: true
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.security.JwtAuthenticationFilter - Authentication set for user: Pranav@rcms, tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.security.JwtAuthenticationFilter - Current tenant context before proceeding with request: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.AuthTenantFilter - AuthTenantFilter processing auth request: /api/auth/logout
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - TenantFilter processing request: /api/auth/logout, Auth header present: true
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - X-TenantID header value: null
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - Current tenant context before processing: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - Keeping existing tenant context: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.cms.config.MediaUploadCorsFilter - MediaUploadCorsFilter processing request: POST /api/auth/logout from origin: http://localhost:3001
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.config.TenantContextInterceptor - TenantContextInterceptor - Request: POST /api/auth/logout - Current tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  c.c.c.CurrentTenantIdentifierResolverImpl - Resolving current tenant identifier: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.c.CurrentTenantIdentifierResolverImpl - Tenant resolution stack trace:
	java.base/java.lang.Thread.getStackTrace(Thread.java:1610)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:30)
	com.cms.config.CurrentTenantIdentifierResolverImpl.resolveCurrentTenantIdentifier(CurrentTenantIdentifierResolverImpl.java:11)
	org.hibernate.internal.SessionFactoryImpl$SessionBuilderImpl.<init>(SessionFactoryImpl.java:1269)
	org.hibernate.internal.SessionFactoryImpl.withOptions(SessionFactoryImpl.java:647)
	org.hibernate.internal.SessionFactoryImpl.buildEntityManager(SessionFactoryImpl.java:746)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:740)
	org.hibernate.internal.SessionFactoryImpl.createEntityManager(SessionFactoryImpl.java:164)
	jdk.internal.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.base/java.lang.reflect.Method.invoke(Method.java:568)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.invokeProxyMethod(AbstractEntityManagerFactoryBean.java:519)
	org.springframework.orm.jpa.AbstractEntityManagerFactoryBean$ManagedEntityManagerFactoryInvocationHandler.invoke(AbstractEntityManagerFactoryBean.java:733)
	jdk.proxy2/jdk.proxy2.$Proxy154.createEntityManager(Unknown Source)
	org.springframework.orm.jpa.EntityManagerFactoryAccessor.createEntityManager(EntityManagerFactoryAccessor.java:169)
	org.springframework.orm.jpa.support.OpenEntityManagerInViewInterceptor.preHandle(OpenEntityManagerInViewInterceptor.java:88)
	org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter.preHandle(WebRequestHandlerInterceptorAdapter.java:57)
	org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.MediaUploadCorsFilter.doFilter(MediaUploadCorsFilter.java:90)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.TenantFilter.doFilter(TenantFilter.java:59)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.AuthTenantFilter.doFilter(AuthTenantFilter.java:50)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.ApiTokenAuthenticationFilter.doFilterInternal(ApiTokenAuthenticationFilter.java:40)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	com.cms.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:90)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.ImageRequestFilter.doFilter(ImageRequestFilter.java:106)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.GlobalHeadersFilter.doFilter(GlobalHeadersFilter.java:72)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	com.cms.config.CustomCorsFilter.doFilter(CustomCorsFilter.java:101)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.base/java.lang.Thread.run(Thread.java:833)

2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Enter: com.cms.controller.AuthController.logout() with argument[s] = []
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.aop.LoggingAspect - Exit: com.cms.controller.AuthController.logout() with result = <200 OK OK,Logged out successfully,[]>
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - Post-handle for request: /api/auth/logout - Current tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG c.c.config.TenantContextInterceptor - TenantContextInterceptor - After completion for request: /api/auth/logout - Final tenant: rcms
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] INFO  com.cms.config.TenantContextHolder - Getting tenant context: rcms, thread: http-nio-0.0.0.0-8071-exec-2
2025-05-26 15:28:40 [http-nio-0.0.0.0-8071-exec-2] DEBUG com.cms.config.TenantFilter - Preserving tenant context after authenticated request: /api/auth/logout, tenant: rcms
