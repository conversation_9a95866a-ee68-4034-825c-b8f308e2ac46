package com.cms.config;

import com.cms.util.TenantUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Special filter for auth endpoints to ensure tenant context is properly maintained
 * during registration and login.
 */
@Component
@Order(0) // Execute before the regular TenantFilter
public class AuthTenantFilter implements Filter {
    
    private static final Logger log = LoggerFactory.getLogger(AuthTenantFilter.class);
    private final TenantUtils tenantUtils;
    
    public AuthTenantFilter(TenantUtils tenantUtils) {
        this.tenantUtils = tenantUtils;
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        String requestURI = req.getRequestURI();
        
        // Only process auth endpoints
        if (requestURI.contains("/auth/")) {
            log.debug("AuthTenantFilter processing auth request: {}", requestURI);
            
            // For registration and login, we need to extract tenant from the username
            if (requestURI.contains("/auth/register") || requestURI.contains("/auth/login")) {
                log.debug("Auth registration or login request detected, tenant context will be managed by the controller");
                
                // Don't clear the tenant context after the request for auth endpoints
                // This allows the controller to manage the tenant context
                chain.doFilter(request, response);
                return;
            }
        }
        
        // For non-auth endpoints, proceed with the regular filter chain
        chain.doFilter(request, response);
    }
}
