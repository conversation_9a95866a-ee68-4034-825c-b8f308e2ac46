package com.cms.controller;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.entity.User;
import com.cms.payload.JwtAuthResponse;
import com.cms.payload.LoginRequest;
import com.cms.payload.SignupRequest;
import com.cms.repository.TenantRepository;
import com.cms.repository.UserRepository;
import com.cms.security.JwtTokenProvider;
import com.cms.service.TenantService;
import com.cms.service.UserService;
import com.cms.util.TenantUtils;
import org.springframework.transaction.annotation.Transactional;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Authentication API")
@Slf4j
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final TenantRepository tenantRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider tokenProvider;
    private final TenantUtils tenantUtils;
    private final TenantService tenantService;
    private final UserService userService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @PostMapping("/login")
    @Operation(
        summary = "Login",
        description = "Authenticate user and return JWT token. Use this token with the Authorize button in Swagger UI.",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Login credentials",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Login",
                        summary = "Login with username and password",
                        value = "{\"username\":\"admin\",\"password\":\"password\"}"
                    )
                },
                schema = @Schema(implementation = LoginRequest.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Login successful, returns JWT token",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "401",
                description = "Invalid credentials",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<JwtAuthResponse> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = tokenProvider.generateToken(authentication);

        return ResponseEntity.ok(new JwtAuthResponse(jwt));
    }

    @PostMapping("/register")
    @Operation(summary = "Register", description = "Register a new user with optional tenant in format username@tenantId")
    @Transactional
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequest signupRequest) {
        log.info("Registering user: {}", signupRequest.getUsername());

        // Clear any existing tenant context to start fresh
        TenantContextHolder.clear();

        // Extract tenant information if present
        String usernameWithTenant = signupRequest.getUsername();
        String username = tenantUtils.extractUsername(usernameWithTenant);
        String tenantSchemaName = tenantUtils.extractTenantSchemaName(usernameWithTenant);

        log.info("Extracted username: {}, tenant: {}", username, tenantSchemaName);

        // Determine the target tenant schema
        String targetTenantId = (tenantSchemaName != null && !tenantSchemaName.isEmpty())
                ? tenantSchemaName
                : TenantContextHolder.getDefaultTenant();

        log.info("Target tenant ID for user registration: {}", targetTenantId);

        // Check if tenant exists or needs to be created
        if (tenantSchemaName != null && !tenantSchemaName.isEmpty()) {
            // Temporarily set context to public for tenant operations
            TenantContextHolder.forceTenantContext(TenantContextHolder.getDefaultTenant());

            if (!tenantRepository.existsBySchemaName(tenantSchemaName)) {
                log.info("Creating new tenant with schema name: {}", tenantSchemaName);

                // Create new tenant
                Tenant newTenant = new Tenant();
                newTenant.setName(tenantSchemaName); // Using schema name as tenant name for simplicity
                newTenant.setSchemaName(tenantSchemaName);
                newTenant.setDescription("Auto-created tenant for user " + username);
                newTenant.setIsActive(true);

                try {
                    // Create the tenant and verify it was created successfully
                    Tenant createdTenant = tenantService.createTenant(newTenant);
                    log.info("Tenant created successfully: {}, ID: {}", tenantSchemaName, createdTenant.getId());

                    // Verify the schema was created
                    jdbcTemplate.execute("SET search_path TO " + tenantSchemaName);
                    List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                            "SELECT table_name FROM information_schema.tables WHERE table_schema = ?",
                            tenantSchemaName);

                    log.info("Tables created in schema {}: {}", tenantSchemaName, tables.size());
                    if (tables.isEmpty()) {
                        log.error("No tables found in schema: {}", tenantSchemaName);
                        return ResponseEntity
                                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body("Error: Schema created but no tables were initialized: " + tenantSchemaName);
                    }

                    // Reset search path to public
                    jdbcTemplate.execute("SET search_path TO public");
                } catch (Exception e) {
                    log.error("Failed to create tenant: {}", e.getMessage(), e);
                    return ResponseEntity
                            .status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body("Error: Failed to create tenant: " + e.getMessage());
                }
            }
        }

        // IMPORTANT: Set the tenant context for user creation
        // This must be done AFTER tenant creation but BEFORE user operations
        TenantContextHolder.forceTenantContext(targetTenantId);
        log.info("Forced tenant context to: {}, current context: {}",
                targetTenantId, TenantContextHolder.getTenantId());

        try {
            // Check if username already exists in the current tenant schema
            log.info("Checking if username '{}' exists in tenant: {}", username, TenantContextHolder.getTenantId());

            // Check username existence using the UserService
            boolean usernameExists = userService.existsByUsername(username);
            log.info("Username exists check result (in current schema): {}", usernameExists);

            if (usernameExists) {
                log.warn("Username '{}' already exists in tenant: {}", username, TenantContextHolder.getTenantId());
                return ResponseEntity
                        .badRequest()
                        .body("Error: Username is already taken in this tenant!");
            }

            // Check email existence using the UserService
            boolean emailExists = userService.existsByEmail(signupRequest.getEmail());
            log.info("Email exists check result (in current schema): {}", emailExists);

            if (emailExists) {
                log.warn("Email '{}' already exists in tenant: {}", signupRequest.getEmail(), TenantContextHolder.getTenantId());
                return ResponseEntity
                        .badRequest()
                        .body("Error: Email is already in use in this tenant!");
            }

            // Create new user's account with just the username part (without tenant)
            User user = new User();
            user.setUsername(username); // Store only the username part
            user.setEmail(signupRequest.getEmail());
            user.setPassword(passwordEncoder.encode(signupRequest.getPassword()));

            // Save the user in the current tenant schema using the UserService
            // Use the tenant-specific createUser method to ensure the user is saved in the correct schema
            log.info("Saving user '{}' in tenant: {}", username, targetTenantId);
            userService.createUser(user, targetTenantId);
            log.info("User registered successfully: {} in tenant: {}", username, targetTenantId);

            return ResponseEntity.status(HttpStatus.CREATED)
                    .body("User registered successfully in tenant: " + targetTenantId);
        } finally {
            // Clear tenant context
            log.info("Clearing tenant context after registration");
            TenantContextHolder.clear();
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "Logout", description = "Logout the current user")
    public ResponseEntity<?> logout() {
        // Clear the security context
        SecurityContextHolder.clearContext();

        return ResponseEntity.ok().body("Logged out successfully");
    }
}
