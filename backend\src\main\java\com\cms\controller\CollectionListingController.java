package com.cms.controller;

import com.cms.dto.CollectionDTO;
import com.cms.entity.CollectionListing;
import com.cms.exception.NoContentException;
import com.cms.mapper.CollectionMapper;
import com.cms.service.CollectionListingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/collections")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Collection", description = "Collection API")
public class CollectionListingController {

    private final CollectionListingService collectionListingService;
    private final CollectionMapper collectionMapper;

    @GetMapping("/getAll")
    @Operation(summary = "Get all collections", description = "Returns a list of all collections")
    public ResponseEntity<List<CollectionListing>> getAllCollections() {
        List<CollectionListing> collections = collectionListingService.getAllCollections();
        if (collections.isEmpty()) {
            throw new NoContentException("No collections found");
        }
        return ResponseEntity.ok(collections);
    }

    @GetMapping("/getAllWithDetails")
    @Operation(summary = "Get all collections with details", description = "Returns a list of all collections with their components, fields, and configs")
    public ResponseEntity<List<CollectionDTO>> getAllCollectionsWithDetails() {
        List<CollectionListing> collections = collectionListingService.getAllCollectionsWithDetails();
        if (collections.isEmpty()) {
            throw new NoContentException("No collections found");
        }
        return ResponseEntity.ok(collectionMapper.toDTOList(collections));
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "Get collection by ID", description = "Returns a collection by its ID or 204 if not found")
    public ResponseEntity<?> getCollectionById(@PathVariable Integer id) {
        Optional<CollectionListing> collectionOpt = collectionListingService.getCollectionById(id);
        if (collectionOpt.isPresent()) {
            return ResponseEntity.ok(collectionOpt.get());
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByIdWithDetails/{id}")
    @Operation(summary = "Get collection by ID with details", description = "Returns a collection by its ID with components, fields, and configs or 204 if not found")
    public ResponseEntity<?> getCollectionByIdWithDetails(@PathVariable Integer id) {
        Optional<CollectionListing> collectionOpt = collectionListingService.getCollectionByIdWithDetails(id);
        if (collectionOpt.isPresent()) {
            return ResponseEntity.ok(collectionMapper.toDTO(collectionOpt.get()));
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByApiId/{apiId}")
    @Operation(summary = "Get collection by API ID", description = "Returns a collection by its API ID or 204 if not found")
    public ResponseEntity<?> getCollectionByApiId(@PathVariable String apiId) {
        Optional<CollectionListing> collectionOpt = collectionListingService.getCollectionByApiId(apiId);
        if (collectionOpt.isPresent()) {
            return ResponseEntity.ok(collectionOpt.get());
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/getByApiIdWithDetails/{apiId}")
    @Operation(summary = "Get collection by API ID with details", description = "Returns a collection by its API ID with components, fields, and configs or 204 if not found")
    public ResponseEntity<?> getCollectionByApiIdWithDetails(@PathVariable String apiId) {
        Optional<CollectionListing> collectionOpt = collectionListingService.getCollectionByApiIdWithDetails(apiId);
        if (collectionOpt.isPresent()) {
            return ResponseEntity.ok(collectionMapper.toDTO(collectionOpt.get()));
        } else {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/create")
    @Operation(
        summary = "Create a new collection",
        description = "Creates a new collection",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Collection object to be created",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Standard Collection",
                        summary = "Standard collection creation example",
                        value = "{\"collectionName\":\"Example Collection\",\"collectionDesc\":\"This is an example collection\",\"collectionApiId\":\"example_collection\",\"additionalInformation\":\"Additional information about the collection\",\"disclaimerText\":\"Disclaimer text for the collection\"}"
                    )
                },
                schema = @Schema(implementation = CollectionListing.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "201",
                description = "Collection created successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "700",
                description = "Null constraint violation",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "701",
                description = "Collection name already exists",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<?> createCollection(@Valid @RequestBody CollectionListing collection) {
        // Check if collection is null
        if (collection == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection is null"));
        }

        // Check if collection name is null
        if (collection.getCollectionName() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection name is null"));
        }

        // Check if collection API ID is null
        if (collection.getCollectionApiId() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection API ID is null"));
        }

        // The service layer will handle the category-specific uniqueness validation
        return ResponseEntity.status(HttpStatus.CREATED).body(collectionListingService.createCollection(collection));
    }

    @PutMapping("/update/{id}")
    @Operation(
        summary = "Update a collection",
        description = "Updates an existing collection",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Collection object with updated values",
            content = @Content(
                mediaType = "application/json",
                examples = {
                    @ExampleObject(
                        name = "Update Collection",
                        summary = "Collection update example",
                        value = "{\"id\":100,\"collectionName\":\"Updated Collection\",\"collectionDesc\":\"This is an updated collection\",\"collectionApiId\":\"updated_collection\",\"additionalInformation\":\"Updated information about the collection\",\"disclaimerText\":\"Updated disclaimer text\"}"
                    )
                },
                schema = @Schema(implementation = CollectionListing.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Collection updated successfully",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "204",
                description = "Collection not found",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "700",
                description = "Null constraint violation",
                content = @Content(mediaType = "application/json")
            ),
            @ApiResponse(
                responseCode = "701",
                description = "Collection name already exists",
                content = @Content(mediaType = "application/json")
            )
        }
    )
    public ResponseEntity<?> updateCollection(@PathVariable Integer id, @Valid @RequestBody CollectionListing collection) {
        // Check if collection is null
        if (collection == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection is null"));
        }

        // Check if collection name is null
        if (collection.getCollectionName() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection name is null"));
        }

        // Check if collection API ID is null
        if (collection.getCollectionApiId() == null) {
            return ResponseEntity.status(700)
                    .body(Map.of("error", "Collection API ID is null"));
        }

        // Check if collection exists
        Optional<CollectionListing> existingCollection = collectionListingService.getCollectionById(id);
        if (existingCollection.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NO_CONTENT)
                    .body(Map.of("message", "No collection found with id: " + id));
        }

        // The service layer will handle the category-specific uniqueness validation
        return ResponseEntity.ok(collectionListingService.updateCollection(id, collection));
    }

    @DeleteMapping("/deleteById/{id}")
    @Operation(summary = "Delete a collection", description = "Deletes a collection or returns 204 if it doesn't exist")
    public ResponseEntity<Map<String, String>> deleteCollection(@PathVariable Integer id) {
        try {
            // Check if the collection exists
            boolean exists = collectionListingService.getCollectionById(id).isPresent();

            if (exists) {
                // Delete the collection
                collectionListingService.deleteCollection(id);
                // Return 204 with success message
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "Successfully deleted collection with id: " + id));
            } else {
                // Return 204 with no content message
                return ResponseEntity.status(HttpStatus.NO_CONTENT)
                        .body(Map.of("message", "No collection found with id: " + id));
            }
        } catch (Exception e) {
            // If an unexpected error occurs, return 500 with error message
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to process delete request for collection with id: " + id + ". Error: " + e.getMessage()));
        }
    }

    @GetMapping("/client/{clientId}")
    @Operation(
        summary = "Get collections by client ID",
        description = "Returns all collections associated with categories belonging to a specific client"
    )
    public ResponseEntity<List<CollectionDTO>> getCollectionsByClientId(@PathVariable Integer clientId) {
        log.info("Getting collections for client ID: {}", clientId);

        try {
            List<CollectionListing> collections = collectionListingService.getCollectionsByClientIdWithDetails(clientId);

            if (collections.isEmpty()) {
                log.info("No collections found for client ID: {}", clientId);
                throw new NoContentException("No collections found for client ID: " + clientId);
            }

            List<CollectionDTO> collectionDTOs = collectionMapper.toDTOList(collections);
            log.info("Found {} collections for client ID: {}", collectionDTOs.size(), clientId);

            return ResponseEntity.ok(collectionDTOs);
        } catch (NoContentException e) {
            // Re-throw NoContentException to be handled by global exception handler
            throw e;
        } catch (Exception e) {
            log.error("Error getting collections for client ID: {}", clientId, e);
            throw e;
        }
    }

    @GetMapping("/category/{categoryId}")
    @Operation(
        summary = "Get collections by category ID",
        description = "Returns all collections associated with a specific category"
    )
    public ResponseEntity<List<CollectionDTO>> getCollectionsByCategoryId(@PathVariable Integer categoryId) {
        log.info("Getting collections for category ID: {}", categoryId);

        try {
            List<CollectionListing> collections = collectionListingService.getCollectionsByCategoryId(categoryId);

            if (collections.isEmpty()) {
                log.info("No collections found for category ID: {}", categoryId);
                throw new NoContentException("No collections found for category ID: " + categoryId);
            }

            List<CollectionDTO> collectionDTOs = collectionMapper.toDTOList(collections);
            log.info("Found {} collections for category ID: {}", collectionDTOs.size(), categoryId);

            return ResponseEntity.ok(collectionDTOs);
        } catch (NoContentException e) {
            // Re-throw NoContentException to be handled by global exception handler
            throw e;
        } catch (Exception e) {
            log.error("Error getting collections for category ID: {}", categoryId, e);
            throw e;
        }
    }

    @GetMapping("/client/{clientId}/category/{categoryName}")
    @Operation(
        summary = "Get collections by client ID and category name",
        description = "Returns all collections associated with a specific client and category (by name)"
    )
    public ResponseEntity<List<CollectionDTO>> getCollectionsByClientIdAndCategoryName(
            @PathVariable Integer clientId,
            @PathVariable String categoryName) {
        log.info("Getting collections for client ID: {} and category name: {}", clientId, categoryName);

        try {
            List<CollectionListing> collections = collectionListingService.getCollectionsByClientIdAndCategoryNameWithDetails(clientId, categoryName);

            if (collections.isEmpty()) {
                log.info("No collections found for client ID: {} and category name: {}", clientId, categoryName);
                throw new NoContentException("No collections found for client ID: " + clientId + " and category name: " + categoryName);
            }

            List<CollectionDTO> collectionDTOs = collectionMapper.toDTOList(collections);
            log.info("Found {} collections for client ID: {} and category name: {}", collectionDTOs.size(), clientId, categoryName);

            return ResponseEntity.ok(collectionDTOs);
        } catch (NoContentException e) {
            // Re-throw NoContentException to be handled by global exception handler
            throw e;
        } catch (Exception e) {
            log.error("Error getting collections for client ID: {} and category name: {}", clientId, categoryName, e);
            throw e;
        }
    }


}
