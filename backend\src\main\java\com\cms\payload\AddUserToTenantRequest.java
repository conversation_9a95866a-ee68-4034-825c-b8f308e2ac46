package com.cms.payload;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * Request payload for adding a user to an existing tenant
 */
@Data
@Schema(description = "Request payload for adding a user to an existing tenant")
public class AddUserToTenantRequest {

    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    @Schema(description = "Username for the new user", example = "john_doe", required = true)
    private String username;

    @NotBlank(message = "Email is required")
    @Size(max = 100, message = "Email must not exceed 100 characters")
    @Email(message = "Email should be valid")
    @Schema(description = "Email address for the new user", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "Password is required")
    @Size(min = 6, max = 120, message = "Password must be between 6 and 120 characters")
    @Schema(description = "Password for the new user", example = "password123", required = true)
    private String password;

    @NotBlank(message = "Tenant schema name is required")
    @Size(min = 1, max = 50, message = "Tenant schema name must be between 1 and 50 characters")
    @Schema(description = "Schema name of the existing tenant", example = "company_abc", required = true)
    private String tenantSchemaName;
}
