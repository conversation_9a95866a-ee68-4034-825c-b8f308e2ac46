package com.cms.repository;

import com.cms.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    Boolean existsByUsername(String username);
    Boolean existsByEmail(String email);

    /**
     * Custom query to check if a username exists in a specific tenant schema
     * This query will be executed in the context of the current schema set by the MultiTenantConnectionProvider
     */
    @Query(value = "SELECT EXISTS(SELECT 1 FROM users WHERE username = :username)", nativeQuery = true)
    Boolean existsByUsernameInCurrentSchema(@Param("username") String username);

    /**
     * Custom query to check if an email exists in a specific tenant schema
     * This query will be executed in the context of the current schema set by the MultiTenantConnectionProvider
     */
    @Query(value = "SELECT EXISTS(SELECT 1 FROM users WHERE email = :email)", nativeQuery = true)
    Boolean existsByEmailInCurrentSchema(@Param("email") String email);
}
