package com.cms.service;

import com.cms.entity.Tenant;
import com.cms.repository.TenantRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Transactional
public class TenantService {

    private static final Logger logger = LoggerFactory.getLogger(TenantService.class);

    @Autowired
    private TenantRepository tenantRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResourceLoader resourceLoader;

    public List<Tenant> getAllTenants() {
        return tenantRepository.findAll();
    }

    public Optional<Tenant> getTenantById(Integer id) {
        return tenantRepository.findById(id);
    }

    public Optional<Tenant> getTenantBySchemaName(String schemaName) {
        return tenantRepository.findBySchemaName(schemaName);
    }

    @Transactional
    public Tenant createTenant(Tenant tenant) {
        logger.info("Creating tenant with schema name: {}", tenant.getSchemaName());

        if (tenantRepository.existsBySchemaName(tenant.getSchemaName())) {
            logger.error("Tenant with schema name {} already exists", tenant.getSchemaName());
            throw new IllegalArgumentException("Tenant with schema name " + tenant.getSchemaName() + " already exists");
        }

        try {
            // Create the schema
            logger.info("Creating schema: {}", tenant.getSchemaName());
            String createSchemaSQL = String.format("CREATE SCHEMA IF NOT EXISTS %s", tenant.getSchemaName());
            logger.debug("Executing SQL: {}", createSchemaSQL);
            jdbcTemplate.execute(createSchemaSQL);

            // Verify schema was created
            List<Map<String, Object>> schemas = jdbcTemplate.queryForList(
                    "SELECT schema_name FROM information_schema.schemata WHERE schema_name = ?",
                    tenant.getSchemaName());

            if (schemas.isEmpty()) {
                logger.error("Failed to create schema: {}", tenant.getSchemaName());
                throw new RuntimeException("Failed to create schema: " + tenant.getSchemaName());
            }

            logger.info("Schema created successfully: {}", tenant.getSchemaName());

            // Initialize the schema with tables
            logger.info("Initializing schema with tables: {}", tenant.getSchemaName());
            initSchema(tenant.getSchemaName());

            // Verify tables were created
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = ?",
                    tenant.getSchemaName());

            logger.info("Tables created in schema {}: {}", tenant.getSchemaName(), tables.size());
            for (Map<String, Object> table : tables) {
                logger.debug("  - {}", table.get("table_name"));
            }

            // Verify critical table structures
            verifyTableStructure(tenant.getSchemaName());

            // Save the tenant
            logger.info("Saving tenant to database: {}", tenant.getName());
            Tenant savedTenant = tenantRepository.save(tenant);
            logger.info("Tenant created successfully with ID: {}", savedTenant.getId());

            return savedTenant;
        } catch (Exception e) {
            logger.error("Error creating tenant: {}", tenant.getSchemaName(), e);
            throw new RuntimeException("Failed to create tenant: " + tenant.getSchemaName(), e);
        }
    }

    private void initSchema(String schemaName) {
        logger.info("Initializing schema: {}", schemaName);
        try {
            // Load schema creation script
            logger.info("Loading schema creation script");
            Resource schemaResource = resourceLoader.getResource("classpath:schema_consolidated.sql");
            if (!schemaResource.exists()) {
                logger.error("Schema creation script not found: classpath:schema_consolidated.sql");
                throw new RuntimeException("Schema creation script not found: classpath:schema_consolidated.sql");
            }

            String schemaSql = new String(FileCopyUtils.copyToByteArray(schemaResource.getInputStream()));
            logger.debug("Schema SQL script loaded, size: {} bytes", schemaSql.length());

            // Replace table creation statements to use the tenant schema
            logger.info("Modifying SQL script to use tenant schema: {}", schemaName);
            schemaSql = schemaSql.replace("CREATE TABLE IF NOT EXISTS ",
                                          String.format("CREATE TABLE IF NOT EXISTS %s.", schemaName));
            schemaSql = schemaSql.replace("CREATE SEQUENCE IF NOT EXISTS ",
                                          String.format("CREATE SEQUENCE IF NOT EXISTS %s.", schemaName));
            schemaSql = schemaSql.replace("CREATE INDEX IF NOT EXISTS ",
                                          String.format("CREATE INDEX IF NOT EXISTS %s_", schemaName));

            // Replace foreign key references to use the tenant schema
            schemaSql = schemaSql.replace("REFERENCES ",
                                          String.format("REFERENCES %s.", schemaName));

            // Execute the schema creation script
            try {
                logger.info("Executing schema creation script for tenant: {}", schemaName);
                jdbcTemplate.execute(schemaSql);
                logger.info("Schema tables created successfully for tenant: {}", schemaName);
            } catch (Exception e) {
                logger.error("Failed to create schema tables for tenant: {}", schemaName, e);
                throw new RuntimeException("Failed to create schema tables for tenant: " + schemaName, e);
            }

            // Load initial data scripts
            logger.info("Loading initial data script");
            Resource dataResource = resourceLoader.getResource("classpath:data.sql");
            if (!dataResource.exists()) {
                logger.error("Data script not found: classpath:data.sql");
                throw new RuntimeException("Data script not found: classpath:data.sql");
            }

            String dataSql = new String(FileCopyUtils.copyToByteArray(dataResource.getInputStream()));
            logger.debug("Data SQL script loaded, size: {} bytes", dataSql.length());

            // Replace insert statements to use the tenant schema
            logger.info("Modifying data SQL script to use tenant schema: {}", schemaName);
            dataSql = dataSql.replace("INSERT INTO ",
                                     String.format("INSERT INTO %s.", schemaName));

            // Execute the data initialization script
            try {
                logger.info("Executing data initialization script for tenant: {}", schemaName);
                jdbcTemplate.execute(dataSql);
                logger.info("Initial data inserted successfully for tenant: {}", schemaName);
            } catch (Exception e) {
                logger.error("Failed to insert initial data for tenant: {}", schemaName, e);
                throw new RuntimeException("Failed to insert initial data for tenant: " + schemaName, e);
            }

            // Load field type data
            logger.info("Loading field type data script");
            Resource fieldTypeResource = resourceLoader.getResource("classpath:db/field_type_insert_data.sql");
            if (!fieldTypeResource.exists()) {
                logger.error("Field type script not found: classpath:db/field_type_insert_data.sql");
                throw new RuntimeException("Field type script not found: classpath:db/field_type_insert_data.sql");
            }

            String fieldTypeSql = new String(FileCopyUtils.copyToByteArray(fieldTypeResource.getInputStream()));
            logger.debug("Field type SQL script loaded, size: {} bytes", fieldTypeSql.length());

            // Replace insert statements to use the tenant schema
            logger.info("Modifying field type SQL script to use tenant schema: {}", schemaName);
            fieldTypeSql = fieldTypeSql.replace("INSERT INTO ",
                                              String.format("INSERT INTO %s.", schemaName));

            // Execute the field type initialization script
            try {
                logger.info("Executing field type initialization script for tenant: {}", schemaName);
                jdbcTemplate.execute(fieldTypeSql);
                logger.info("Field type data inserted successfully for tenant: {}", schemaName);
            } catch (Exception e) {
                logger.error("Failed to insert field type data for tenant: {}", schemaName, e);
                throw new RuntimeException("Failed to insert field type data for tenant: " + schemaName, e);
            }

            // Load field config data
            logger.info("Loading field config data script");
            Resource fieldConfigResource = resourceLoader.getResource("classpath:db/field_config_insert_data.sql");
            if (!fieldConfigResource.exists()) {
                logger.error("Field config script not found: classpath:db/field_config_insert_data.sql");
                throw new RuntimeException("Field config script not found: classpath:db/field_config_insert_data.sql");
            }

            String fieldConfigSql = new String(FileCopyUtils.copyToByteArray(fieldConfigResource.getInputStream()));
            logger.debug("Field config SQL script loaded, size: {} bytes", fieldConfigSql.length());

            // Replace insert statements to use the tenant schema
            logger.info("Modifying field config SQL script to use tenant schema: {}", schemaName);
            fieldConfigSql = fieldConfigSql.replace("INSERT INTO ",
                                                 String.format("INSERT INTO %s.", schemaName));

            // Execute the field config initialization script
            try {
                logger.info("Executing field config initialization script for tenant: {}", schemaName);
                jdbcTemplate.execute(fieldConfigSql);
                logger.info("Field config data inserted successfully for tenant: {}", schemaName);
            } catch (Exception e) {
                logger.error("Failed to insert field config data for tenant: {}", schemaName, e);
                throw new RuntimeException("Failed to insert field config data for tenant: " + schemaName, e);
            }

            logger.info("Schema initialization completed successfully for tenant: {}", schemaName);

        } catch (IOException e) {
            logger.error("Failed to initialize schema: {}", schemaName, e);
            throw new RuntimeException("Failed to initialize schema: " + schemaName, e);
        }
    }

    /**
     * Verify the structure of critical tables in the tenant schema
     * This method checks that all required columns exist in the tables
     *
     * @param schemaName The name of the tenant schema to verify
     */
    private void verifyTableStructure(String schemaName) {
        logger.info("Verifying table structure for schema: {}", schemaName);

        try {
            // Verify collection_listing table has category_id column
            List<Map<String, Object>> columns = jdbcTemplate.queryForList(
                    "SELECT column_name FROM information_schema.columns " +
                    "WHERE table_schema = ? AND table_name = 'collection_listing' AND column_name = 'category_id'",
                    schemaName);

            if (columns.isEmpty()) {
                logger.error("Missing category_id column in collection_listing table for schema: {}", schemaName);
                logger.info("Adding missing category_id column to collection_listing table");

                // Add the missing column
                jdbcTemplate.execute(String.format(
                        "ALTER TABLE %s.collection_listing ADD COLUMN category_id INT REFERENCES %s.category(id)",
                        schemaName, schemaName));

                logger.info("Added category_id column to collection_listing table in schema: {}", schemaName);
            } else {
                logger.info("Verified category_id column exists in collection_listing table for schema: {}", schemaName);
            }

            // Verify other critical tables and columns as needed
            // ...

        } catch (Exception e) {
            logger.error("Error verifying table structure for schema {}: {}", schemaName, e.getMessage(), e);
            throw new RuntimeException("Failed to verify table structure for schema: " + schemaName, e);
        }
    }

    @Transactional
    public void deleteTenant(Integer id) {
        Optional<Tenant> tenantOpt = tenantRepository.findById(id);
        if (tenantOpt.isPresent()) {
            Tenant tenant = tenantOpt.get();

            // Drop the schema
            jdbcTemplate.execute(String.format("DROP SCHEMA IF EXISTS %s CASCADE", tenant.getSchemaName()));

            // Delete the tenant
            tenantRepository.delete(tenant);
        } else {
            throw new IllegalArgumentException("Tenant with ID " + id + " not found");
        }
    }
}
