package com.cms.service;

import com.cms.config.TenantContextHolder;
import com.cms.entity.User;
import com.cms.repository.UserRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final UserRepository userRepository;

    /**
     * Check if a username exists in the specified tenant schema
     *
     * @param username The username to check
     * @param tenantId The tenant ID to check in
     * @return true if the username exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username, String tenantId) {
        log.debug("Checking if username '{}' exists in tenant: {}", username, tenantId);

        // Save the current tenant context
        String currentTenant = TenantContextHolder.getTenantId();

        try {
            // Set the tenant context to the specified tenant
            TenantContextHolder.setTenantId(tenantId);

            // Check if the username exists in the specified tenant
            boolean exists = userRepository.existsByUsernameInCurrentSchema(username);
            log.debug("Username '{}' exists in tenant {}: {}", username, tenantId, exists);

            return exists;
        } finally {
            // Restore the original tenant context
            TenantContextHolder.setTenantId(currentTenant);
        }
    }

    /**
     * Check if a username exists in the current tenant schema
     *
     * @param username The username to check
     * @return true if the username exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return existsByUsername(username, TenantContextHolder.getTenantId());
    }

    /**
     * Check if an email exists in the specified tenant schema
     *
     * @param email The email to check
     * @param tenantId The tenant ID to check in
     * @return true if the email exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email, String tenantId) {
        log.debug("Checking if email '{}' exists in tenant: {}", email, tenantId);

        // Save the current tenant context
        String currentTenant = TenantContextHolder.getTenantId();

        try {
            // Set the tenant context to the specified tenant
            TenantContextHolder.setTenantId(tenantId);

            // Check if the email exists in the specified tenant
            boolean exists = userRepository.existsByEmailInCurrentSchema(email);
            log.debug("Email '{}' exists in tenant {}: {}", email, tenantId, exists);

            return exists;
        } finally {
            // Restore the original tenant context
            TenantContextHolder.setTenantId(currentTenant);
        }
    }

    /**
     * Check if an email exists in the current tenant schema
     *
     * @param email The email to check
     * @return true if the email exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return existsByEmail(email, TenantContextHolder.getTenantId());
    }

    /**
     * Create a new user in the current tenant schema
     *
     * @param user The user to create
     * @return The created user
     */
    @Transactional
    public User createUser(User user) {
        String tenantId = TenantContextHolder.getTenantId();
        log.debug("Creating user '{}' in tenant: {}", user.getUsername(), tenantId);

        // Explicitly set the tenant context to ensure it's properly propagated
        TenantContextHolder.setTenantId(tenantId);

        try {
            log.debug("Saving user in tenant schema: {}", tenantId);
            User savedUser = userRepository.save(user);
            log.debug("User saved successfully in tenant: {}, user ID: {}", tenantId, savedUser.getId());
            return savedUser;
        } catch (Exception e) {
            log.error("Error saving user in tenant {}: {}", tenantId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Create a new user in the specified tenant schema
     *
     * @param user The user to create
     * @param tenantId The tenant ID to create the user in
     * @return The created user
     */
    /**
     * Create a new user in the specified tenant schema
     * This method ensures the user is saved in the correct tenant schema
     *
     * @param user The user to create
     * @param tenantId The tenant ID to create the user in
     * @return The created user
     */
    @Transactional
    public User createUser(User user, String tenantId) {
        log.info("Creating user '{}' in tenant: {}", user.getUsername(), tenantId);

        // Save the current tenant context
        String currentTenant = TenantContextHolder.getTenantId();
        log.info("Current tenant context before switch: {}", currentTenant);

        try {
            // Force the tenant context to the specified tenant
            log.info("Forcing tenant context to: {}", tenantId);
            TenantContextHolder.forceTenantContext(tenantId);

            // Verify the database connection is using the correct schema
            verifyDatabaseSchema(tenantId);

            // Create the user in the specified tenant
            log.info("Saving user in tenant schema: {}", tenantId);
            User createdUser = userRepository.save(user);
            log.info("User '{}' created successfully in tenant: {}, user ID: {}",
                    user.getUsername(), tenantId, createdUser.getId());

            return createdUser;
        } catch (Exception e) {
            log.error("Error saving user '{}' in tenant {}: {}",
                    user.getUsername(), tenantId, e.getMessage(), e);
            throw e;
        } finally {
            // Restore the original tenant context
            log.info("Restoring tenant context to: {}", currentTenant);
            TenantContextHolder.setTenantId(currentTenant);
        }
    }

    /**
     * Verify that the database connection is using the correct schema
     *
     * @param expectedSchema The expected schema name
     */
    private void verifyDatabaseSchema(String expectedSchema) {
        try {
            // Use a native query to check the current schema
            String currentSchema = (String) entityManager.createNativeQuery("SELECT current_schema()").getSingleResult();
            log.info("Current database schema: {}, expected: {}", currentSchema, expectedSchema);

            if (!expectedSchema.equals(currentSchema)) {
                log.error("CRITICAL: Database schema mismatch! Expected: {}, Actual: {}", expectedSchema, currentSchema);

                // Try to force the schema change with a direct SQL command
                log.info("Attempting to force schema change to: {}", expectedSchema);
                entityManager.createNativeQuery("SET search_path TO " + expectedSchema).executeUpdate();

                // Verify the change was successful
                String newSchema = (String) entityManager.createNativeQuery("SELECT current_schema()").getSingleResult();
                log.info("Schema after forced change: {}", newSchema);

                if (!expectedSchema.equals(newSchema)) {
                    throw new RuntimeException("Failed to set database schema to: " + expectedSchema);
                }
            }
        } catch (Exception e) {
            log.error("Error verifying database schema: {}", e.getMessage(), e);
        }
    }

    /**
     * Find a user by username in the current tenant schema
     *
     * @param username The username to find
     * @return The user if found, otherwise empty
     */
    @Transactional(readOnly = true)
    public java.util.Optional<User> findByUsername(String username) {
        String tenantId = TenantContextHolder.getTenantId();
        log.info("Finding user by username '{}' in tenant: {}", username, tenantId);

        try {
            // Verify the current schema matches the tenant ID
            verifyDatabaseSchema(tenantId);

            // Execute the query
            java.util.Optional<User> user = userRepository.findByUsername(username);

            if (user.isPresent()) {
                log.info("User found: {}, ID: {}", user.get().getUsername(), user.get().getId());
            } else {
                log.info("No user found with username '{}' in tenant: {}", username, tenantId);
            }

            return user;
        } catch (Exception e) {
            log.error("Error finding user '{}' in tenant {}: {}", username, tenantId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Find a user by username in the specified tenant schema
     *
     * @param username The username to find
     * @param tenantId The tenant ID to find the user in
     * @return The user if found, otherwise empty
     */
    @Transactional(readOnly = true)
    public java.util.Optional<User> findByUsername(String username, String tenantId) {
        log.debug("Finding user by username '{}' in tenant: {}", username, tenantId);

        // Save the current tenant context
        String currentTenant = TenantContextHolder.getTenantId();

        try {
            // Set the tenant context to the specified tenant
            TenantContextHolder.setTenantId(tenantId);

            // Find the user in the specified tenant
            java.util.Optional<User> user = userRepository.findByUsername(username);
            log.debug("User '{}' found in tenant {}: {}", username, tenantId, user.isPresent());

            return user;
        } finally {
            // Restore the original tenant context
            TenantContextHolder.setTenantId(currentTenant);
        }
    }
}
