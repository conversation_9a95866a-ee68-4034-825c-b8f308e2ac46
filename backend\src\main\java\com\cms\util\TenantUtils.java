package com.cms.util;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.repository.TenantRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class TenantUtils {

    private final TenantRepository tenantRepository;
    private static final String DEFAULT_TENANT = "public";

    public TenantUtils(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    /**
     * Parses a username that may contain a tenant identifier in the format username@tenantId
     * Sets the tenant in the TenantContextHolder and returns the username without the tenant part
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The username without the tenant part
     */
    public String parseUsernameAndSetTenant(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            TenantContextHolder.setTenantId(DEFAULT_TENANT);
            return usernameWithTenant;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            String username = parts[0];
            String tenantSchemaName = parts[1];

            log.debug("Attempting to find tenant with schema name: {}", tenantSchemaName);

            // First try exact match
            Optional<Tenant> tenant = tenantRepository.findBySchemaName(tenantSchemaName);

            // If not found, try case-insensitive search by getting all tenants and filtering
            if (!tenant.isPresent()) {
                log.debug("Tenant not found with exact match, trying case-insensitive search");
                List<Tenant> allTenants = tenantRepository.findAll();
                tenant = allTenants.stream()
                    .filter(t -> t.getSchemaName().equalsIgnoreCase(tenantSchemaName))
                    .findFirst();

                if (tenant.isPresent()) {
                    log.debug("Found tenant with case-insensitive match: {}", tenant.get().getSchemaName());
                }
            }

            if (tenant.isPresent() && tenant.get().getIsActive()) {
                String actualSchemaName = tenant.get().getSchemaName();
                log.debug("Setting tenant context to: {}", actualSchemaName);
                TenantContextHolder.setTenantId(actualSchemaName);
            } else {
                log.warn("Tenant not found or inactive: {}, using default tenant", tenantSchemaName);
                TenantContextHolder.setTenantId(DEFAULT_TENANT);
            }

            return username;
        } else {
            // No tenant specified, use default
            TenantContextHolder.setTenantId(DEFAULT_TENANT);
            return usernameWithTenant;
        }
    }

    /**
     * Extracts the tenant schema name from a username in the format username@tenantId
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The tenant schema name or null if not present
     */
    public String extractTenantSchemaName(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            return null;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            return parts[1];
        }

        return null;
    }

    /**
     * Extracts the tenant schema name from an email domain
     * Converts email domain to a safe schema name by replacing dots with underscores
     * and converting to lowercase
     *
     * @param email The email address
     * @return The tenant schema name derived from email domain or null if invalid email
     */
    public String extractTenantSchemaFromEmail(String email) {
        if (email == null || email.isEmpty()) {
            return null;
        }

        // Basic email validation
        if (!email.contains("@") || email.indexOf("@") != email.lastIndexOf("@")) {
            log.warn("Invalid email format: {}", email);
            return null;
        }

        String[] parts = email.split("@", 2);
        if (parts.length == 2 && !parts[1].isEmpty()) {
            String domain = parts[1].toLowerCase();

            // Sanitize domain for schema name
            String schemaName = sanitizeDomainForSchema(domain);

            log.debug("Extracted tenant schema '{}' from email domain '{}'", schemaName, domain);
            return schemaName;
        }

        log.warn("Could not extract domain from email: {}", email);
        return null;
    }

    /**
     * Sanitizes a domain name to be safe for use as a database schema name
     * - Converts to lowercase
     * - Replaces dots with underscores
     * - Removes or replaces other unsafe characters
     * - Ensures it starts with a letter or underscore
     *
     * @param domain The domain name to sanitize
     * @return The sanitized schema name
     */
    public String sanitizeDomainForSchema(String domain) {
        if (domain == null || domain.isEmpty()) {
            return null;
        }

        // Convert to lowercase and replace dots with underscores
        String sanitized = domain.toLowerCase()
                .replace(".", "_")
                .replace("-", "_")
                .replaceAll("[^a-z0-9_]", "_"); // Replace any other non-alphanumeric chars with underscore

        // Remove consecutive underscores
        sanitized = sanitized.replaceAll("_+", "_");

        // Remove leading/trailing underscores
        sanitized = sanitized.replaceAll("^_+|_+$", "");

        // Ensure it starts with a letter or underscore (database schema naming requirement)
        if (!sanitized.isEmpty() && Character.isDigit(sanitized.charAt(0))) {
            sanitized = "tenant_" + sanitized;
        }

        // Ensure minimum length
        if (sanitized.length() < 2) {
            sanitized = "tenant_" + sanitized;
        }

        // Limit maximum length (PostgreSQL schema names have a 63 character limit)
        if (sanitized.length() > 60) {
            sanitized = sanitized.substring(0, 60);
        }

        log.debug("Sanitized domain '{}' to schema name '{}'", domain, sanitized);
        return sanitized;
    }

    /**
     * Determines the tenant schema name for a user registration
     * Priority:
     * 1. If username contains @tenant format, use that tenant
     * 2. Otherwise, derive tenant from email domain
     *
     * @param username The username (may contain @tenant format)
     * @param email The user's email address
     * @return The tenant schema name to use
     */
    public String determineTenantSchemaForRegistration(String username, String email) {
        log.debug("Determining tenant schema for username: '{}', email: '{}'", username, email);

        // First check if username has explicit tenant specification
        String explicitTenant = extractTenantSchemaName(username);
        if (explicitTenant != null && !explicitTenant.isEmpty()) {
            log.info("Using explicit tenant from username: {}", explicitTenant);
            return explicitTenant;
        }

        // Otherwise, derive from email domain
        String emailBasedTenant = extractTenantSchemaFromEmail(email);
        if (emailBasedTenant != null && !emailBasedTenant.isEmpty()) {
            log.info("Using tenant derived from email domain: {}", emailBasedTenant);
            return emailBasedTenant;
        }

        log.warn("Could not determine tenant schema for username: '{}', email: '{}'", username, email);
        return null;
    }

    /**
     * Extracts the username part from a string in the format username@tenantId
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The username without the tenant part
     */
    public String extractUsername(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            return usernameWithTenant;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            return parts[0];
        }

        return usernameWithTenant;
    }
}
