package com.cms.util;

import com.cms.config.TenantContextHolder;
import com.cms.entity.Tenant;
import com.cms.repository.TenantRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class TenantUtils {

    private final TenantRepository tenantRepository;
    private static final String DEFAULT_TENANT = "public";

    public TenantUtils(TenantRepository tenantRepository) {
        this.tenantRepository = tenantRepository;
    }

    /**
     * Parses a username that may contain a tenant identifier in the format username@tenantId
     * Sets the tenant in the TenantContextHolder and returns the username without the tenant part
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The username without the tenant part
     */
    public String parseUsernameAndSetTenant(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            TenantContextHolder.setTenantId(DEFAULT_TENANT);
            return usernameWithTenant;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            String username = parts[0];
            String tenantSchemaName = parts[1];

            log.debug("Attempting to find tenant with schema name: {}", tenantSchemaName);

            // First try exact match
            Optional<Tenant> tenant = tenantRepository.findBySchemaName(tenantSchemaName);

            // If not found, try case-insensitive search by getting all tenants and filtering
            if (!tenant.isPresent()) {
                log.debug("Tenant not found with exact match, trying case-insensitive search");
                List<Tenant> allTenants = tenantRepository.findAll();
                tenant = allTenants.stream()
                    .filter(t -> t.getSchemaName().equalsIgnoreCase(tenantSchemaName))
                    .findFirst();

                if (tenant.isPresent()) {
                    log.debug("Found tenant with case-insensitive match: {}", tenant.get().getSchemaName());
                }
            }

            if (tenant.isPresent() && tenant.get().getIsActive()) {
                String actualSchemaName = tenant.get().getSchemaName();
                log.debug("Setting tenant context to: {}", actualSchemaName);
                TenantContextHolder.setTenantId(actualSchemaName);
            } else {
                log.warn("Tenant not found or inactive: {}, using default tenant", tenantSchemaName);
                TenantContextHolder.setTenantId(DEFAULT_TENANT);
            }

            return username;
        } else {
            // No tenant specified, use default
            TenantContextHolder.setTenantId(DEFAULT_TENANT);
            return usernameWithTenant;
        }
    }

    /**
     * Extracts the tenant schema name from a username in the format username@tenantId
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The tenant schema name or null if not present
     */
    public String extractTenantSchemaName(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            return null;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            return parts[1];
        }

        return null;
    }

    /**
     * Extracts the username part from a string in the format username@tenantId
     *
     * @param usernameWithTenant The username that may contain a tenant identifier
     * @return The username without the tenant part
     */
    public String extractUsername(String usernameWithTenant) {
        if (usernameWithTenant == null || usernameWithTenant.isEmpty()) {
            return usernameWithTenant;
        }

        String[] parts = usernameWithTenant.split("@", 2);
        if (parts.length == 2) {
            return parts[0];
        }

        return usernameWithTenant;
    }
}
