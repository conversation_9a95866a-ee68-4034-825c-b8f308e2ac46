# Database Configuration
spring.datasource.url=***************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Multi-tenancy Configuration
multitenancy.default-tenant=public
spring.jpa.properties.hibernate.multiTenancy=SCHEMA
spring.jpa.properties.hibernate.tenant_identifier_resolver=com.cms.config.CurrentTenantIdentifierResolverImpl
spring.jpa.properties.hibernate.multi_tenant_connection_provider=com.cms.config.MultiTenantConnectionProviderImpl

# Initialize the database using SQL scripts
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema_consolidated.sql,classpath:db/field_type_data.sql,classpath:db/tenants_table.sql,classpath:db/clients_table.sql
spring.sql.init.data-locations=classpath:data.sql,classpath:db/field_type_insert_data.sql,classpath:db/field_config_insert_data.sql
spring.sql.init.continue-on-error=true
spring.jpa.defer-datasource-initialization=true
spring.sql.init.separator=;

# Database Indexing
spring.sql.init.platform=postgresql

# Server Configuration
server.port=8071
server.servlet.context-path=/api
server.address=0.0.0.0

# CORS Configuration is now handled by WebConfig and cors-config.properties

# JWT Configuration
jwt.expiration=86400000

# Swagger Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.defaultModelsExpandDepth=0
springdoc.swagger-ui.defaultModelExpandDepth=2
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
springdoc.model-converters.pageable-converter.enabled=true

# Essential Swagger settings
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.api-docs.version=OPENAPI_3_0
springdoc.swagger-ui.supported-submit-methods=get,put,post,delete,options,head,patch,trace
springdoc.swagger-ui.validator-url=none
springdoc.swagger-ui.syntax-highlight.activated=true
springdoc.swagger-ui.deep-linking=true

# Enable Swagger UI for all profiles
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# Content Negotiation Configuration
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.favor-path-extension=false
spring.mvc.contentnegotiation.media-types.json=application/json
spring.mvc.contentnegotiation.media-types.*=*/*
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true

# Jackson Configuration
spring.jackson.deserialization.accept-single-value-as-array=true
spring.jackson.deserialization.fail-on-unknown-properties=false
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.date-format=yyyy-MM-dd'T'HH:mm:ss.SSSZ
spring.jackson.time-zone=UTC

# Logging Configuration
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.com.cms=DEBUG
logging.level.com.cms.config=TRACE
logging.level.com.cms.service=TRACE
logging.level.com.cms.controller=TRACE

# Set specific loggers to higher levels for tenant-related classes
logging.level.com.cms.config.TenantContextHolder=TRACE
logging.level.com.cms.config.MultiTenantConnectionProviderImpl=TRACE
logging.level.com.cms.config.CurrentTenantIdentifierResolverImpl=TRACE
logging.level.com.cms.service.UserService=TRACE
logging.level.org.hibernate.context.spi=TRACE
logging.level.org.hibernate.engine.jdbc.connections.spi=TRACE

# Log to file for easier debugging
logging.file.name=logs/multi-tenant-app.log
logging.file.max-size=10MB
logging.file.max-history=5
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=75MB

# File Storage Configuration
file.upload-dir=media-uploads
# Base URLs with placeholders - will be dynamically replaced with system IP
file.base-url=http://${server.ip:localhost}:${server.port}/api
server.external-url=http://${server.ip:localhost}:${server.port}