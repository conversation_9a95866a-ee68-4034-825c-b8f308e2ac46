-- Insert default tenant
INSERT INTO tenants (id, name, schema_name, is_active, description)
VALUES (1, 'Public Tenant', 'public', true, 'Default public tenant')
ON CONFLICT (id) DO NOTHING;

-- Insert sample user (password: password)
INSERT INTO users (id, username, email, password, is_active, is_logged_in)
VALUES (1, 'admin', '<EMAIL>', '$2a$10$eDIJO.xkAYdMQXkYUY/YCOGQrjlXbriPH0TZTjZxl9uHEsA8Y5D.6', true, false)
ON CONFLICT (username) DO NOTHING;

-- Category, component, and collection data has been removed
-- You can add your own data here as needed

-- Note: The sample content entry below is commented out since it references collection_id
-- Uncomment and modify it once you've created your own collections

-- Insert sample content entry
-- INSERT INTO content_entries (id, collection_id, data_json)
-- VALUES
-- (1, 1, '{"heading": "Sample Blog Post", "paragraphs": ["This is a sample paragraph.", "This is another paragraph."], "images": ["image1.jpg", "image2.jpg"]}')
-- ON CONFLICT (id) DO NOTHING;
