-- =============================================
-- DATABASE SCHEMA DEFINITION
-- =============================================

-- Clients table (must appear before category)
CREATE TABLE IF NOT EXISTS clients (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_clients_name ON clients(name);

-- Tenants table
CREATE TABLE IF NOT EXISTS tenants (
    id INT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_by VARC<PERSON>R(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_tenants_schema_name ON tenants(schema_name);
CREATE INDEX IF NOT EXISTS idx_tenants_is_active ON tenants(is_active);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(120) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_logged_in BOOLEAN DEFAULT false,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- API Tokens
CREATE TABLE IF NOT EXISTS api_tokens (
    id BIGINT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    token_value VARCHAR(255) NOT NULL UNIQUE,
    description VARCHAR(200),
    expires_at TIMESTAMP NOT NULL,
    last_used_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    user_id BIGINT NOT NULL REFERENCES users(id),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Media-related tables
CREATE TABLE IF NOT EXISTS media_folders (
    id INTEGER PRIMARY KEY,
    folder_name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INTEGER REFERENCES media_folders(id),
    user_id BIGINT REFERENCES users(id),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS media (
    id INTEGER PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    width INTEGER,
    height INTEGER,
    duration INTEGER,
    alt_text VARCHAR(255),
    description TEXT,
    public_url VARCHAR(255),
    share_token VARCHAR(100),
    is_public BOOLEAN DEFAULT false,
    folder_id INTEGER REFERENCES media_folders(id),
    user_id BIGINT REFERENCES users(id),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Field Types and Configs
CREATE TABLE IF NOT EXISTS field_types (
    id INT PRIMARY KEY,
    field_type_name VARCHAR(255) NOT NULL UNIQUE,
    field_type_desc TEXT,
    display_name VARCHAR(255),
    help_text TEXT,
    logo_image_path VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS config_types (
    id INT PRIMARY KEY,
    config_type_name VARCHAR(255) NOT NULL UNIQUE,
    config_type_desc TEXT,
    display_name VARCHAR(255),
    additional_info TEXT,
    disclaimer_text TEXT,
    placeholder_text TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS field_configs (
    id INT PRIMARY KEY,
    field_type_id INT REFERENCES field_types(id),
    config_type_id INT REFERENCES config_types(id),
    config_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    value_type VARCHAR(50),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Category (after clients)
CREATE TABLE IF NOT EXISTS category (
    id INT PRIMARY KEY,
    category_name VARCHAR(255) NOT NULL,
    client_id INTEGER REFERENCES clients(id),
    parent_category_id INTEGER REFERENCES category(id),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_category_client_id ON category(client_id);
CREATE INDEX IF NOT EXISTS idx_category_parent_category_id ON category(parent_category_id);

-- Component-related
CREATE TABLE IF NOT EXISTS component_listing (
    id INT PRIMARY KEY,
    component_name VARCHAR(255) NOT NULL UNIQUE,
    component_display_name VARCHAR(255),
    component_api_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    get_url VARCHAR(255),
    post_url VARCHAR(255),
    update_url VARCHAR(255),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS component_fields (
    id INT PRIMARY KEY,
    component_id INT REFERENCES component_listing(id),
    field_type_id INT REFERENCES field_types(id),
    display_preference INT,
    dependent_on INT REFERENCES component_fields(id),
    additional_information TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS component_components (
    id INT PRIMARY KEY,
    parent_component_id INT REFERENCES component_listing(id),
    child_component_id INT REFERENCES component_listing(id),
    display_preference INT,
    is_repeatable BOOLEAN DEFAULT false,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT true,
    additional_information TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS component_field_config (
    id INT PRIMARY KEY,
    component_field_id INT REFERENCES component_fields(id),
    field_config_id INT REFERENCES field_configs(id),
    field_config_value VARCHAR(255),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Collection-related
CREATE TABLE IF NOT EXISTS collection_listing (
    id INT PRIMARY KEY,
    collection_name VARCHAR(255) NOT NULL,
    collection_desc TEXT,
    additional_information TEXT,
    disclaimer_text TEXT,
    collection_api_id VARCHAR(255),
    category_id INT REFERENCES category(id),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP,
    CONSTRAINT uk_collection_name_category_id UNIQUE (collection_name, category_id)
);

CREATE TABLE IF NOT EXISTS collection_components (
    id INT PRIMARY KEY,
    collection_id INT REFERENCES collection_listing(id),
    component_id INT REFERENCES component_listing(id),
    display_preference INT,
    additional_information TEXT,
    is_repeatable BOOLEAN DEFAULT false,
    min_repeat_occurrences INT,
    max_repeat_occurrences INT,
    is_active BOOLEAN DEFAULT true,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS collection_fields (
    id INT PRIMARY KEY,
    collection_id INT REFERENCES collection_listing(id),
    field_type_id INT REFERENCES field_types(id),
    display_preference INT,
    dependent_on INT REFERENCES collection_fields(id),
    additional_information TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS collection_field_config (
    id INT PRIMARY KEY,
    collection_field_id INT REFERENCES collection_fields(id),
    field_config_id INT REFERENCES field_configs(id),
    field_config_value VARCHAR(255),
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- Content entries
CREATE TABLE IF NOT EXISTS content_entries (
    id INT PRIMARY KEY,
    collection_id INT REFERENCES collection_listing(id),
    data_json JSONB,
    created_by VARCHAR(50),
    created_at TIMESTAMP,
    modified_by VARCHAR(50),
    modified_at TIMESTAMP
);

-- =============================================
-- SEQUENCE DEFINITIONS
-- =============================================
CREATE SEQUENCE IF NOT EXISTS cms_tenant_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_user_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_client_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_category_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_component_listing_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_component_field_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_component_field_config_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_config_type_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_field_config_seq START WITH 500 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_field_type_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_collection_listing_seq START WITH 1000 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_collection_component_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_colleciton_field_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_collection_field_config_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_content_entry_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_media_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_media_folder_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_api_token_seq START WITH 100 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS cms_component_component_seq START WITH 1000 INCREMENT BY 1;

-- You can later do sequence adjustments from Java code or SQL scripts manually
