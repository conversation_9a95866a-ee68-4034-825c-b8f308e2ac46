# Tenant User Management Guide

This guide explains how to manage users within existing tenant schemas in the CMS application.

## Overview

The CMS application now supports multiple ways to add users to tenant schemas:

1. **Original Method**: Using `username@schema` format during registration
2. **New Method**: Adding users to existing tenant schemas without the `@schema` format

## Available Endpoints

### 1. Add User to Existing Tenant

**Endpoint**: `POST /api/auth/add-user-to-tenant`

**Description**: Add a new user to an existing tenant schema without using the `@schema` format.

**Request Body**:
```json
{
  "username": "john_doe",
  "email": "<EMAIL>", 
  "password": "password123",
  "tenantSchemaName": "company_abc"
}
```

**Success Response**:
```json
"User 'john_doe' added successfully to tenant 'company_abc'!"
```

**Error Responses**:
- `400 Bad Request`: If tenant doesn't exist, is inactive, or user/email already exists
- `500 Internal Server Error`: If there's a server error during user creation

### 2. List Users in Tenant

**Endpoint**: `POST /api/auth/list-users-in-tenant`

**Description**: List all users in a specific tenant schema.

**Request Body**:
```json
{
  "tenantSchemaName": "company_abc"
}
```

**Success Response**:
```json
[
  {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00",
    "modifiedAt": "2024-01-01T10:00:00"
  },
  {
    "id": 2,
    "username": "john_doe",
    "email": "<EMAIL>",
    "isActive": true,
    "createdAt": "2024-01-02T11:00:00",
    "modifiedAt": "2024-01-02T11:00:00"
  }
]
```

## Usage Examples

### Example 1: Create a Tenant and Add Multiple Users

1. **Create the first user and tenant** (using original method):
```bash
curl -X POST http://localhost:8071/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin@company_abc",
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

2. **Add additional users to the existing tenant**:
```bash
curl -X POST http://localhost:8071/api/auth/add-user-to-tenant \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe",
    "email": "<EMAIL>",
    "password": "password123",
    "tenantSchemaName": "company_abc"
  }'
```

3. **Add another user**:
```bash
curl -X POST http://localhost:8071/api/auth/add-user-to-tenant \
  -H "Content-Type: application/json" \
  -d '{
    "username": "jane_smith",
    "email": "<EMAIL>",
    "password": "password456",
    "tenantSchemaName": "company_abc"
  }'
```

### Example 2: List All Users in a Tenant

```bash
curl -X POST http://localhost:8071/api/auth/list-users-in-tenant \
  -H "Content-Type: application/json" \
  -d '{
    "tenantSchemaName": "company_abc"
  }'
```

### Example 3: User Login

All users can login using their username (without the @schema part):

```bash
curl -X POST http://localhost:8071/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "john_doe@company_abc",
    "password": "password123"
  }'
```

## Key Features

### 1. Tenant Validation
- Validates that the target tenant exists before adding users
- Checks that the tenant is active
- Provides clear error messages for invalid tenants

### 2. User Uniqueness
- Ensures usernames are unique within each tenant schema
- Ensures email addresses are unique within each tenant schema
- Users with the same username can exist in different tenants

### 3. Security
- Passwords are encrypted using the same security standards
- All tenant operations are properly isolated
- Tenant context is properly managed and cleared after operations

### 4. Logging
- Comprehensive logging for debugging and auditing
- Tracks all user creation and tenant operations
- Logs tenant context switches for troubleshooting

## Best Practices

1. **Use descriptive tenant schema names**: Choose meaningful names like `company_abc`, `client_xyz`
2. **Consistent naming**: Use consistent naming conventions for usernames within tenants
3. **Email uniqueness**: Ensure email addresses are unique within each tenant
4. **Password security**: Use strong passwords for all users
5. **Regular auditing**: Use the list users endpoint to regularly audit tenant users

## Troubleshooting

### Common Issues

1. **Tenant not found**: Ensure the tenant schema name is correct and the tenant exists
2. **User already exists**: Check if the username or email already exists in the target tenant
3. **Inactive tenant**: Ensure the target tenant is active
4. **Authentication issues**: Users must login with `username@schema` format

### Debugging

Check the application logs for detailed information about:
- Tenant context switches
- User creation operations
- Database schema verification
- Error details and stack traces
