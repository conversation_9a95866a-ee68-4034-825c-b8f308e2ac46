@echo off
setlocal enabledelayedexpansion

REM Test script for login/logout functionality with debugging
REM This script tests the login/logout cycle to identify the "bad credentials" issue

set BASE_URL=http://localhost:8071
set USERNAME=admin@public
set PASSWORD=password

echo === Testing Login/Logout Cycle with Debugging ===
echo Base URL: %BASE_URL%
echo Username: %USERNAME%
echo.

echo === Debug 1: Check if user exists ===
curl -X GET "%BASE_URL%/test/auth/debug-user/admin" > debug1.json
echo Debug response saved to debug1.json
type debug1.json
echo.

echo === Debug 2: Set tenant to public ===
curl -X POST "%BASE_URL%/test/auth/set-tenant/public" > debug2.json
echo Debug response saved to debug2.json
type debug2.json
echo.

echo === Test 1: Initial Login ===
curl -X POST -H "Content-Type: application/json" -d "{\"username\":\"%USERNAME%\",\"password\":\"%PASSWORD%\"}" "%BASE_URL%/auth/login" > login1.json
echo Login 1 response saved to login1.json
type login1.json
echo.

echo === Debug 3: Check user status after login ===
curl -X GET "%BASE_URL%/test/auth/debug-user/admin" > debug3.json
echo Debug response saved to debug3.json
type debug3.json
echo.

echo === Test 2: Logout (without token for now) ===
curl -X POST "%BASE_URL%/auth/logout" > logout.json
echo Logout response saved to logout.json
type logout.json
echo.

echo === Debug 4: Check user status after logout ===
curl -X GET "%BASE_URL%/test/auth/debug-user/admin" > debug4.json
echo Debug response saved to debug4.json
type debug4.json
echo.

echo === Test 3: Try Re-login ===
curl -X POST -H "Content-Type: application/json" -d "{\"username\":\"%USERNAME%\",\"password\":\"%PASSWORD%\"}" "%BASE_URL%/auth/login" > login2.json
echo Login 2 response saved to login2.json
type login2.json
echo.

echo === Debug 5: Check user status after re-login ===
curl -X GET "%BASE_URL%/test/auth/debug-user/admin" > debug5.json
echo Debug response saved to debug5.json
type debug5.json
echo.

echo === Test Complete ===
echo Check all the files to see what's happening:
echo - debug1.json: Initial user status
echo - login1.json: First login attempt
echo - debug3.json: User status after first login
echo - logout.json: Logout response
echo - debug4.json: User status after logout
echo - login2.json: Second login attempt (this should work now!)
echo - debug5.json: User status after re-login

pause
