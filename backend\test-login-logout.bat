@echo off
setlocal enabledelayedexpansion

REM Test script for login/logout functionality
REM This script tests the login/logout cycle to identify the "bad credentials" issue

set BASE_URL=http://localhost:8071
set USERNAME=admin@public
set PASSWORD=password

echo === Testing Login/Logout Cycle ===
echo Base URL: %BASE_URL%
echo Username: %USERNAME%
echo.

echo === Test 1: Initial Login ===
curl -X POST -H "Content-Type: application/json" -d "{\"username\":\"%USERNAME%\",\"password\":\"%PASSWORD%\"}" "%BASE_URL%/auth/login" > login1.json
echo Login 1 response saved to login1.json
type login1.json
echo.

echo === Test 2: Extract Token and Logout ===
REM Note: You'll need to manually extract the token from login1.json for the logout test
echo Please extract the token from login1.json and run:
echo curl -X POST -H "Authorization: Bearer YOUR_TOKEN" "%BASE_URL%/auth/logout"
echo.

echo === Test 3: Try Re-login ===
curl -X POST -H "Content-Type: application/json" -d "{\"username\":\"%USERNAME%\",\"password\":\"%PASSWORD%\"}" "%BASE_URL%/auth/login" > login2.json
echo Login 2 response saved to login2.json
type login2.json
echo.

echo === Test Complete ===
echo Check login1.json and login2.json to compare responses
echo If login2.json shows "bad credentials", there's an issue with the logout process

pause
