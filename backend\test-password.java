import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class TestPassword {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        String plainPassword = "password";
        String storedHash = "$2a$10$eDIJO.xkAYdMQXkYUY/YCOGQrjlXbriPH0TZTjZxl9uHEsA8Y5D.6";
        
        System.out.println("Plain password: " + plainPassword);
        System.out.println("Stored hash: " + storedHash);
        System.out.println("Password matches: " + encoder.matches(plainPassword, storedHash));
        
        // Generate a new hash for comparison
        String newHash = encoder.encode(plainPassword);
        System.out.println("New hash: " + newHash);
        System.out.println("New hash matches: " + encoder.matches(plainPassword, newHash));
    }
}
