
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import { TenantProvider } from "@/components/tenant/TenantProvider";
import { CMSLayout } from "./components/layout/CMSLayout";
import Login from "./pages/Login";
import Register from "./pages/Register";
import NotFound from "./pages/NotFound";
import Dashboard from "./pages/Dashboard";
import ContentTypes from "./pages/ContentTypes";
import ContentTypesNew from "./pages/ContentTypesNew";
import ContentTypeCreate from "./pages/ContentTypeCreate";
import ContentTypeEdit from "./pages/ContentTypeEdit";
import CategoryCollections from "./pages/CategoryCollections";
import ContentManager from "./pages/ContentManager";

import Clients from "./pages/Clients";
import ContentEntryEdit from "./pages/ContentEntryEdit";
import ContentEntryCreate from "./pages/ContentEntryCreate";
import Components from "./pages/Components";
import ComponentCreate from "./pages/ComponentCreate";
import ComponentEdit from "./pages/ComponentEdit";
import MediaLibraryFixed from "./pages/MediaLibraryFixed";
import MediaRenameTest from "./pages/MediaRenameTest";
import MediaImageTest from "./pages/MediaImageTest";
import MediaDebug from "./pages/MediaDebug";
import MediaReplace from "./pages/MediaReplace";
import ApiTokenSettings from "./pages/ApiTokenSettings";
import ClientCollections from "./pages/ClientCollections";
import ParentCategories from "./pages/ParentCategories";

const queryClient = new QueryClient();

const App = () => (
  <ThemeProvider defaultTheme="system" storageKey="cms-theme">
    <QueryClientProvider client={queryClient}>
      <TenantProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner position="top-right" />
          <BrowserRouter>
        <Routes>
          {/* Auth Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* CMS Routes - Wrapped in Layout */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<CMSLayout><Dashboard /></CMSLayout>} />

          {/* Content Type Builder Routes */}
          <Route path="/content-types" element={<CMSLayout><ContentTypesNew /></CMSLayout>} />
          <Route path="/content-types/create" element={<CMSLayout><ContentTypeCreate /></CMSLayout>} />
          <Route path="/content-types/edit/:id" element={<CMSLayout><ContentTypeEdit /></CMSLayout>} />
          <Route path="/content-types/category/:categoryId" element={<CMSLayout><CategoryCollections /></CMSLayout>} />
          
          

          {/* Content Manager Routes */}
          <Route path="/content-manager" element={<CMSLayout><ContentManager /></CMSLayout>} />
          <Route path="/content-manager/:collectionId" element={<CMSLayout><ContentManager /></CMSLayout>} />
          <Route path="/content-manager/:collectionId/create" element={<CMSLayout><ContentEntryCreate /></CMSLayout>} />
          <Route path="/content-manager/:collectionId/edit/:entryId" element={<CMSLayout><ContentEntryEdit /></CMSLayout>} />

{/* CLients */}
          <Route path="/clients" element={<CMSLayout><Clients /></CMSLayout>} />
          <Route path="/clients/:clientId/" element={<CMSLayout><ClientCollections /></CMSLayout>} />


          {/* Parent Category */}
<Route path= "/parent-categories" element={<CMSLayout><ParentCategories /></CMSLayout>} />

          {/* Components Routes */}
          <Route path="/components" element={<CMSLayout><Components /></CMSLayout>} />
          <Route path="/components/new" element={<CMSLayout><ComponentCreate /></CMSLayout>} />
          <Route path="/components/edit/:id" element={<CMSLayout><ComponentEdit /></CMSLayout>} />

          {/* Media Library Routes */}
          <Route path="/media-library" element={<CMSLayout><MediaLibraryFixed /></CMSLayout>} />
          <Route path="/media-library/replace" element={<CMSLayout><MediaReplace /></CMSLayout>} />
          <Route path="/media-rename-test" element={<CMSLayout><MediaRenameTest /></CMSLayout>} />
          <Route path="/media-image-test" element={<CMSLayout><MediaImageTest /></CMSLayout>} />
          <Route path="/media-debug" element={<CMSLayout><MediaDebug /></CMSLayout>} />

          {/* Settings Routes */}
          <Route path="/settings" element={<CMSLayout><ApiTokenSettings /></CMSLayout>} />
          <Route path="/settings/api-tokens" element={<Navigate to="/settings" replace />} />

          {/* Not Found Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </TenantProvider>
    </QueryClientProvider>
  </ThemeProvider>
);

export default App;
