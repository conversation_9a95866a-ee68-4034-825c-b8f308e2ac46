import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/store';

interface AuthStatusDebugProps {
  className?: string;
}

export default function AuthStatusDebug({ className = '' }: AuthStatusDebugProps) {
  const { user, token, isAuthenticated } = useAuthStore();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateDebugInfo = () => {
      const localStorageToken = localStorage.getItem('cms_token');
      
      let tokenInfo = null;
      if (token) {
        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          tokenInfo = {
            username: payload.sub,
            tenant: payload.tenant,
            issuedAt: new Date(payload.iat * 1000).toLocaleString(),
            expiresAt: new Date(payload.exp * 1000).toLocaleString(),
            ageSeconds: Math.floor((Date.now() - (payload.iat * 1000)) / 1000),
          };
        } catch (e) {
          tokenInfo = { error: 'Failed to parse token' };
        }
      }

      setDebugInfo({
        authStore: {
          isAuthenticated,
          hasUser: !!user,
          hasToken: !!token,
          username: user?.username,
          tenant: user?.tenant,
        },
        localStorage: {
          hasToken: !!localStorageToken,
          tokenMatches: token === localStorageToken,
        },
        token: tokenInfo,
        currentPath: window.location.pathname,
        timestamp: new Date().toLocaleString(),
      });
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [user, token, isAuthenticated]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-xs z-50 ${className}`}
      >
        Show Auth Debug
      </button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50 ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold text-sm">Auth Status</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700 text-xs"
        >
          ✕
        </button>
      </div>
      
      <div className="text-xs space-y-2">
        <div>
          <strong>Store:</strong> {debugInfo.authStore?.isAuthenticated ? '✅ Authenticated' : '❌ Not authenticated'}
        </div>
        <div>
          <strong>User:</strong> {debugInfo.authStore?.username || 'None'}
        </div>
        <div>
          <strong>Tenant:</strong> {debugInfo.authStore?.tenant || 'None'}
        </div>
        <div>
          <strong>Token:</strong> {debugInfo.authStore?.hasToken ? '✅ Present' : '❌ Missing'}
        </div>
        <div>
          <strong>LocalStorage:</strong> {debugInfo.localStorage?.hasToken ? '✅ Present' : '❌ Missing'}
        </div>
        <div>
          <strong>Sync:</strong> {debugInfo.localStorage?.tokenMatches ? '✅ Synced' : '❌ Out of sync'}
        </div>
        {debugInfo.token && (
          <div>
            <strong>Token Age:</strong> {debugInfo.token.ageSeconds}s
          </div>
        )}
        <div className="text-gray-500 text-xs">
          Updated: {debugInfo.timestamp}
        </div>
      </div>
    </div>
  );
}
