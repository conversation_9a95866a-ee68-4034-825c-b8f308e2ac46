import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { categoriesApi } from '@/lib/api';
import { useClickedParentCategory } from '@/lib/store';

interface BasicCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  initialCategoryId?: string;
  clientId?: string;
  parentCategoryId?: string;
}

export default function BasicCollectionDialog({ isOpen, onClose, onSave, initialCategoryId, clientId, parentCategoryId }: BasicCollectionDialogProps) {
  const [name, setName] = useState('');
  const [apiIdSingular, setApiIdSingular] = useState('');
  const [apiIdPlural, setApiIdPlural] = useState('');
  const [categoryId, setCategoryId] = useState('');
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Get parent category ID from store
  const { parentCategoryId: storeParentCategoryId } = useClickedParentCategory();

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setName('');
      setApiIdSingular('');
      setApiIdPlural('');

      // Set the category ID if provided, otherwise reset it
      if (initialCategoryId) {
        console.log('Setting initial category ID:', initialCategoryId);
        setCategoryId(initialCategoryId);
      } else {
        setCategoryId('');
      }

      // Determine which parent category ID and client ID to use
      const effectiveParentCategoryId = parentCategoryId || storeParentCategoryId;
      const effectiveClientId = clientId;

      console.log('Dialog opened with:', {
        parentCategoryId: effectiveParentCategoryId,
        clientId: effectiveClientId,
        initialCategoryId,
        storeParentCategoryId,
        propsParentCategoryId: parentCategoryId,
        propsClientId: clientId
      });

      // Always start by trying to fetch all categories, then filter if needed
      setLoadingCategories(true);
      console.log('Fetching all categories first...');

      categoriesApi.getAll()
        .then(response => {
          console.log('All categories API response:', response);
          const data = response.data;

          if (Array.isArray(data)) {
            // Format categories for display
            let formattedCategories = data.map(category => ({
              id: category.id,
              categoryName: category.categoryName || category.category_name || category.name || `Category ${category.id}`,
              client: category.client,
              parentCategory: category.parentCategory
            }));

            console.log(`Found ${formattedCategories.length} total categories:`, formattedCategories);

            // Try to filter by client if we have a client ID
            if (effectiveClientId) {
              const clientFilteredCategories = formattedCategories.filter(category =>
                category.client && category.client.id && category.client.id.toString() === effectiveClientId.toString()
              );

              if (clientFilteredCategories.length > 0) {
                console.log(`Filtered to ${clientFilteredCategories.length} categories for client ${effectiveClientId}`);
                formattedCategories = clientFilteredCategories;

                // Further filter by parent category if we have one
                if (effectiveParentCategoryId) {
                  const parentFilteredCategories = formattedCategories.filter(category =>
                    category.parentCategory &&
                    category.parentCategory.id &&
                    category.parentCategory.id.toString() === effectiveParentCategoryId.toString()
                  );

                  if (parentFilteredCategories.length > 0) {
                    console.log(`Further filtered to ${parentFilteredCategories.length} child categories for parent ${effectiveParentCategoryId}`);
                    formattedCategories = parentFilteredCategories;
                  } else {
                    console.log('No child categories found for parent, keeping client categories');
                  }
                }
              } else {
                console.log('No categories found for client, keeping all categories');
              }
            }

            setCategories(formattedCategories);
          } else {
            console.warn('Categories data is not in expected format:', data);
            setCategories([]);
          }
        })
        .catch(error => {
          console.error('Error fetching categories:', error);
          setCategories([]);
        })
        .finally(() => {
          setLoadingCategories(false);
        });
    }
  }, [isOpen, parentCategoryId, clientId, storeParentCategoryId, initialCategoryId]);

  // Handle name change to auto-generate API IDs
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setName(value);

    if (value) {
      const apiId = value.toLowerCase().replace(/[^a-z0-9]/g, '_');
      setApiIdSingular(apiId);
      setApiIdPlural(apiId + 's');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !apiIdSingular || !apiIdPlural) {
      return;
    }

    setIsLoading(true);

    const data = {
      name,
      apiIdSingular,
      apiIdPlural,
      categoryId,
      draftAndPublish: false,
      isInternationally: false
    };

    onSave(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-card rounded-lg shadow-lg w-full max-w-md p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>

        <h2 className="text-xl font-bold mb-6">Create Collection</h2>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="mb-4">
              <Label htmlFor="name" className="block mb-2 font-medium">Display Name</Label>
              <Input
                id="name"
                value={name}
                onChange={handleNameChange}
                placeholder="e.g. Restaurant, Article, Product..."
                className="w-full h-10 px-3 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div className="mb-4">
              <Label htmlFor="category" className="block mb-2 font-medium">Category</Label>
              {loadingCategories ? (
                <div className="flex items-center gap-2 p-2 border rounded-md">
                  <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-primary animate-spin"></div>
                  <span className="text-sm">Loading categories from database...</span>
                </div>
              ) : (
                <>
                  <select
                    id="category"
                    value={categoryId}
                    onChange={(e) => setCategoryId(e.target.value)}
                    className="w-full border border-gray-300 rounded-md p-2 h-10"
                  >
                    <option value="">None</option>
                    {categories.length > 0 ? (
                      categories.map((category) => (
                        <option key={category.id} value={category.id.toString()}>
                          {category.categoryName || category.category_name || category.name || `Category ${category.id}`}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>No categories found in database</option>
                    )}
                  </select>
                  <div className="mt-1 text-xs text-gray-500">
                    {categories.length > 0 ?
                      `${categories.length} categories found in database` :
                      "No categories found in the database"}
                  </div>
                </>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <Label htmlFor="apiIdSingular" className="block mb-2 font-medium">API ID (Singular)</Label>
                <Input
                  id="apiIdSingular"
                  value={apiIdSingular}
                  onChange={(e) => setApiIdSingular(e.target.value)}
                  placeholder="e.g. restaurant"
                  className="w-full h-10 px-3 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <Label htmlFor="apiIdPlural" className="block mb-2 font-medium">API ID (Plural)</Label>
                <Input
                  id="apiIdPlural"
                  value={apiIdPlural}
                  onChange={(e) => setApiIdPlural(e.target.value)}
                  placeholder="e.g. restaurants"
                  className="w-full h-10 px-3 border border-gray-300 rounded-md"
                  required
                />
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="w-24"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !name || !apiIdSingular || !apiIdPlural}
                className="w-36 bg-indigo-400 hover:bg-indigo-500"
              >
                {isLoading ? 'Creating...' : 'Create Collection'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
