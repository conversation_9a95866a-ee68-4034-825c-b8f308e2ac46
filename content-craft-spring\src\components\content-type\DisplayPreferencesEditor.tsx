import React, { useState, useEffect, useCallback } from 'react';
import { Save, X } from 'lucide-react';
import { Field, FieldTypeEnum } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { collectionOrderingApi, collectionComponentsApi, collectionFieldsApi } from '@/lib/api';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface DisplayPreferencesEditorProps {
  isOpen: boolean;
  onClose: () => void;
  collectionId: string;
  fields: Field[];
  onSuccess: () => void;
}

interface FieldWithDisplayPreference extends Field {
  displayPreference: number;
  isComponent: boolean;
}

export default function DisplayPreferencesEditor({
  isOpen,
  onClose,
  collectionId,
  fields,
  onSuccess,
}: DisplayPreferencesEditorProps) {
  const { toast } = useToast();
  const [orderedFields, setOrderedFields] = useState<FieldWithDisplayPreference[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  // Function to fetch the latest data from the backend
  const fetchLatestData = useCallback(async () => {
    if (!collectionId) return;

    // Check if we have a timestamp for the last update
    const lastUpdateTimestamp = localStorage.getItem(`display_prefs_updated_${collectionId}`);

    // Add a cache-busting parameter to force a fresh request
    const cacheBuster = lastUpdateTimestamp || Date.now();

    try {
      console.log('Fetching latest display preferences data for collection:', collectionId);
      // Use a custom API call with cache busting to ensure we get fresh data
      const response = await collectionOrderingApi.getOrderedItems(`${collectionId}?t=${cacheBuster}`);
      console.log('Latest ordered items data:', response.data);

      // Extract components and fields from the response
      const { components, fields: responseFields } = response.data;

      // Process the fields data to match our expected format
      const processedFields: FieldWithDisplayPreference[] = [];

      // Process components
      if (components && Array.isArray(components)) {
        console.log('Processing components from API:', components);
        components.forEach(component => {
          // Find the matching field in our current fields array
          const matchingField = fields.find(f =>
            f.type === FieldTypeEnum.COMPONENT &&
            f.attributes?.collectionComponentId?.toString() === component.id.toString()
          );

          if (matchingField) {
            // Ensure display preference is a number and in the correct format (multiple of 10)
            let displayPref = component.displayPreference;
            if (typeof displayPref !== 'number' || displayPref % 10 !== 0) {
              // If not a multiple of 10, round to the nearest multiple of 10
              displayPref = Math.round(displayPref / 10) * 10;
              if (displayPref === 0) displayPref = 10; // Ensure minimum value is 10
            }

            processedFields.push({
              ...matchingField,
              displayPreference: displayPref,
              isComponent: true
            });
            console.log(`Component ${matchingField.name} (ID: ${component.id}) display preference: ${displayPref}`);
          } else {
            console.warn(`No matching field found for component ID: ${component.id}`);
          }
        });
      }

      // Process regular fields
      if (responseFields && Array.isArray(responseFields)) {
        console.log('Processing regular fields from API:', responseFields);
        responseFields.forEach(field => {
          // Find the matching field in our current fields array
          const matchingField = fields.find(f =>
            f.id?.toString() === field.id.toString() && f.type !== FieldTypeEnum.COMPONENT
          );

          if (matchingField) {
            // Ensure display preference is a number and in the correct format (multiple of 10)
            let displayPref = field.displayPreference;
            if (typeof displayPref !== 'number' || displayPref % 10 !== 0) {
              // If not a multiple of 10, round to the nearest multiple of 10
              displayPref = Math.round(displayPref / 10) * 10;
              if (displayPref === 0) displayPref = 10; // Ensure minimum value is 10
            }

            // Ensure the field has additionalInformation from the API response
            let additionalInformation = matchingField.additionalInformation;
            if (field.additionalInformation && !additionalInformation) {
              additionalInformation = field.additionalInformation;
            }

            processedFields.push({
              ...matchingField,
              displayPreference: displayPref,
              isComponent: false,
              additionalInformation: additionalInformation
            });
            console.log(`Field ${matchingField.name} (ID: ${field.id}) display preference: ${displayPref}`);
          } else {
            console.warn(`No matching field found for field ID: ${field.id}`);
          }
        });
      }

      // Add any fields that weren't in the response but are in our fields prop
      fields.forEach((field, index) => {
        const isComponent = field.type === FieldTypeEnum.COMPONENT;
        const collectionComponentId = field.attributes?.collectionComponentId?.toString();

        // Check if this field is already in processedFields
        const alreadyProcessed = processedFields.some(pf =>
          (isComponent && pf.attributes?.collectionComponentId?.toString() === collectionComponentId) ||
          (!isComponent && pf.id === field.id)
        );

        if (!alreadyProcessed) {
          // Add this field with a default display preference
          processedFields.push({
            ...field,
            displayPreference: (processedFields.length + 1) * 10,
            isComponent
          });
          console.log(`Added missing field ${field.name} with default display preference: ${(processedFields.length) * 10}`);
        }
      });

      // Sort by display preference
      const sorted = [...processedFields].sort(
        (a, b) => a.displayPreference - b.displayPreference
      );

      setOrderedFields(sorted);

    } catch (error) {
      console.error('Error fetching latest display preferences:', error);

      // Fall back to using the fields prop
      initializeFromProps();
    }
  }, [collectionId, fields]);

  // Function to initialize from props (fallback)
  const initializeFromProps = useCallback(() => {
    if (!fields || fields.length === 0) return;

    try {
      // Convert fields to include display preference
      const fieldsWithPreference = fields.map((field, index) => {
        // Check if this is a component field
        const isComponent = field.type === FieldTypeEnum.COMPONENT;

        // Get the display preference
        // For components, check if it has a collectionComponentId attribute
        let displayPref = field.displayPreference;

        if (!displayPref) {
          // If no display preference is set, use a sequential value
          displayPref = (index + 1) * 10;
        } else {
          // Ensure existing display preferences follow the 10, 20, 30 format
          displayPref = Math.max(10, Math.round(displayPref / 10) * 10);
        }

        // Log the field and its display preference
        console.log(`Field: ${field.name}, Type: ${isComponent ? 'Component' : 'Field'}, Display Preference: ${displayPref}`);

        return {
          ...field,
          displayPreference: displayPref,
          isComponent,
        };
      });

      // Sort by display preference
      const sorted = [...fieldsWithPreference].sort(
        (a, b) => a.displayPreference - b.displayPreference
      );

      setOrderedFields(sorted);
    } catch (error) {
      console.error('Error initializing fields with display preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to initialize fields. Please try again.',
        variant: 'destructive',
      });
    }
  }, [fields, toast]);

  // Initialize fields with display preferences
  useEffect(() => {
    if (isOpen) {
      // When the dialog opens, fetch the latest data
      fetchLatestData();
    }
  }, [isOpen, fetchLatestData]);

  // Function to get field type display name
  const getFieldTypeDisplayName = useCallback((field: Field): string => {
    switch (field.type) {
      case FieldTypeEnum.TEXT:
        return 'Text';
      case FieldTypeEnum.RICH_TEXT:
        return 'Rich Text';
      case FieldTypeEnum.NUMBER:
        return 'Number';
      case FieldTypeEnum.DATE:
        return 'Date';
      case FieldTypeEnum.BOOLEAN:
        return 'Boolean';
      case FieldTypeEnum.ENUM:
        return 'Dropdown';
      case FieldTypeEnum.MEDIA:
        return 'Media';
      case FieldTypeEnum.JSON:
        return 'JSON';
      case FieldTypeEnum.EMAIL:
        return 'Email';
      case FieldTypeEnum.PASSWORD:
        return 'Password';
      case FieldTypeEnum.RELATION:
        return 'Relation';
      case FieldTypeEnum.COMPONENT:
        return 'Component';
      default:
        return field.type || 'Unknown';
    }
  }, []);

  // Note: Up/down movement functions removed as we're using direct display preference input

  // Function to update display preference directly
  const updateDisplayPreference = useCallback((index: number, value: string) => {
    let numValue = parseInt(value, 10);
    if (isNaN(numValue)) return;

    // Ensure the value follows the 10, 20, 30 format
    // Round to the nearest 10
    numValue = Math.max(10, Math.round(numValue / 10) * 10);

    console.log(`Normalizing display preference to: ${numValue}`);

    setOrderedFields(prevFields => {
      const newFields = [...prevFields];
      newFields[index].displayPreference = numValue;
      return newFields;
    });
  }, []);

  // Function to save the updated display preferences
  const saveDisplayPreferences = useCallback(async () => {
    setIsSaving(true);
    try {
      // Validate that all fields have valid display preferences
      const invalidFields = orderedFields.filter(field => {
        const pref = field.displayPreference;
        return isNaN(pref) || pref < 0;
      });

      if (invalidFields.length > 0) {
        throw new Error(`Invalid display preferences for fields: ${invalidFields.map(f => f.name).join(', ')}`);
      }

      // Extract component and field IDs in the correct order
      const componentIds: number[] = [];
      const fieldIds: number[] = [];

      // Sort by display preference before extracting IDs
      const sortedFields = [...orderedFields].sort((a, b) => {
        // Use 999 as default for undefined display preferences to push them to the end
        const prefA = typeof a.displayPreference === 'number' ? a.displayPreference : 999;
        const prefB = typeof b.displayPreference === 'number' ? b.displayPreference : 999;
        return prefA - prefB;
      });

      // Always normalize display preferences to the 10, 20, 30 format
      // This ensures consistent display order values
      const normalizedFields = sortedFields.map((field, index) => {
        // Create a new object with updated display preference
        // Ensure display preference is a multiple of 10
        const displayPref = (index + 1) * 10; // This guarantees the 10, 20, 30 sequence

        console.log(`Normalizing ${field.isComponent ? 'component' : 'field'} ${field.name} from ${field.displayPreference} to ${displayPref}`);

        return {
          ...field,
          displayPreference: displayPref
        };
      });

      // Use the normalized fields for the API call
      normalizedFields.forEach((field) => {
        if (field.type === FieldTypeEnum.COMPONENT && field.componentId) {
          // For components, we need the collection component ID
          if (field.attributes?.collectionComponentId) {
            const componentId = parseInt(field.attributes.collectionComponentId.toString(), 10);
            console.log(`Adding component ID ${componentId} for component ${field.name}`);
            componentIds.push(componentId);
          } else {
            console.warn(`Component ${field.name} has no collectionComponentId attribute`);
          }
        } else if (field.id) {
          // For regular fields, we need the field ID
          const fieldId = parseInt(field.id, 10);
          console.log(`Adding field ID ${fieldId} for field ${field.name}`);
          fieldIds.push(fieldId);
        } else {
          console.warn(`Field ${field.name} has no ID`);
        }
      });

      console.log('Saving display preferences with:', {
        componentIds,
        fieldIds,
        normalizedFields: normalizedFields.map(f => ({ name: f.name, pref: f.displayPreference }))
      });

      // Validate that we have at least some IDs to update
      if (componentIds.length === 0 && fieldIds.length === 0) {
        console.warn('No component or field IDs found to update');
        toast({
          title: 'Warning',
          description: 'No fields or components found to update',
          variant: 'default',
        });
        return;
      }

      try {
        // Update each component individually using the collection component update API
        const componentUpdatePromises = [];
        const fieldUpdatePromises = [];

        // Process components
        for (const field of normalizedFields) {
          if (field.type === FieldTypeEnum.COMPONENT && field.attributes?.collectionComponentId) {
            const collectionComponentId = field.attributes.collectionComponentId.toString();
            const displayPreference = field.displayPreference;

            console.log(`Updating component ID ${collectionComponentId} with display preference: ${displayPreference}`, {
              componentId: field.componentId,
              name: field.name,
              isRepeatable: field.attributes?.isRepeatable,
              collectionId
            });

            // Create the update payload
            const updatePayload = {
              id: parseInt(collectionComponentId),
              displayPreference: displayPreference,
              // Include required fields for the API
              collection: { id: parseInt(collectionId) },
              component: { id: parseInt(field.componentId || '0') },
              // Preserve existing properties
              isActive: true,
              isRepeatable: field.attributes?.isRepeatable || false,
              minRepeatOccurrences: field.attributes?.minRepeatOccurrences || null,
              maxRepeatOccurrences: field.attributes?.maxRepeatOccurrences || null
            };

            // Ensure display preference is a multiple of 10
            if (updatePayload.displayPreference % 10 !== 0) {
              updatePayload.displayPreference = Math.round(updatePayload.displayPreference / 10) * 10;
              if (updatePayload.displayPreference === 0) updatePayload.displayPreference = 10;
            }

            // Log the update payload
            console.log(`Update payload for component ${collectionComponentId}:`, updatePayload);

            // Add to promises array
            componentUpdatePromises.push(
              collectionComponentsApi.update(collectionComponentId, updatePayload)
                .then(response => {
                  console.log(`Successfully updated component ${collectionComponentId}:`, response.data);
                  return response;
                })
                .catch(error => {
                  console.error(`Error updating component ${collectionComponentId}:`, error);
                  console.error('Failed payload:', updatePayload);
                  throw error;
                })
            );
          } else if (field.id && !field.id.startsWith('component_')) {
            // Handle regular fields
            const fieldId = field.id.toString();
            const displayPreference = field.displayPreference;

            console.log(`Updating field ID ${fieldId} with display preference: ${displayPreference}`);

            // Create the update payload for fields
            // Preserve the field's additional information with all its properties
            let additionalInfo = null;
            if (field.additionalInformation) {
              additionalInfo = field.additionalInformation;
            } else {
              // If additionalInformation is not available, reconstruct it from field properties
              additionalInfo = JSON.stringify({
                name: field.name,
                apiId: field.apiId,
                description: field.description || '',
                required: field.required || false,
                unique: field.unique || false,
                type: field.type,
                attributes: field.attributes || {},
                validations: field.validations || {}
              });
            }

            const updatePayload = {
              id: parseInt(fieldId),
              displayPreference: displayPreference,
              // Include required fields for the API
              collection: { id: parseInt(collectionId) },
              fieldType: { id: parseInt(field.fieldTypeId || '0') },
              // Preserve the field's additional information
              additionalInformation: additionalInfo
            };

            // Ensure display preference is a multiple of 10
            if (updatePayload.displayPreference % 10 !== 0) {
              updatePayload.displayPreference = Math.round(updatePayload.displayPreference / 10) * 10;
              if (updatePayload.displayPreference === 0) updatePayload.displayPreference = 10;
            }

            // Log the update payload
            console.log(`Update payload for field ${fieldId}:`, updatePayload);

            // Add to field promises array
            fieldUpdatePromises.push(
              collectionFieldsApi.update(fieldId, updatePayload)
                .then(response => {
                  console.log(`Successfully updated field ${fieldId}:`, response.data);
                  return response;
                })
                .catch(error => {
                  console.error(`Error updating field ${fieldId}:`, error);
                  console.error('Failed payload:', updatePayload);
                  throw error;
                })
            );
          }
        }

        // Wait for all component updates to complete
        if (componentUpdatePromises.length > 0) {
          const results = await Promise.allSettled(componentUpdatePromises);

          // Check for any failures
          const failures = results.filter(result => result.status === 'rejected');
          if (failures.length > 0) {
            console.error(`${failures.length} component updates failed:`, failures);
            throw new Error(`Failed to update ${failures.length} components`);
          }

          console.log('All component display preferences updated successfully');
        } else {
          console.log('No components to update');
        }

        // Handle field updates
        if (fieldUpdatePromises.length > 0) {
          const fieldResults = await Promise.allSettled(fieldUpdatePromises);

          // Check for any failures
          const fieldFailures = fieldResults.filter(result => result.status === 'rejected');
          if (fieldFailures.length > 0) {
            console.error(`${fieldFailures.length} field updates failed:`, fieldFailures);
            throw new Error(`Failed to update ${fieldFailures.length} fields`);
          }

          console.log('All field display preferences updated successfully');
        } else {
          console.log('No fields to update');
        }

        toast({
          title: 'Success',
          description: 'Display preferences updated successfully',
        });

        // Update the local state with normalized values
        setOrderedFields(normalizedFields);

        // Fetch the updated data to ensure UI reflects the latest changes
        try {
          const refreshResponse = await collectionOrderingApi.getOrderedItems(collectionId);
          console.log('Refreshed data after save:', refreshResponse.data);

          // Store the successful update timestamp in localStorage
          // This will be used to force a refresh when the dialog reopens
          localStorage.setItem(`display_prefs_updated_${collectionId}`, Date.now().toString());
        } catch (refreshError) {
          console.error('Error refreshing data after save:', refreshError);
          // Continue with the process even if refresh fails
        }

        // Call the success callback to refresh data in the parent component
        onSuccess();

        // Close the dialog after a short delay to show the success message
        setTimeout(() => {
          onClose();
        }, 500);
      } catch (apiError) {
        console.error('API Error updating display preferences:', apiError);
        toast({
          title: 'API Error',
          description: apiError instanceof Error ? apiError.message : 'Failed to update display preferences',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error preparing display preferences update:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to prepare display preferences update',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [collectionId, onClose, onSuccess, orderedFields, toast]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] bg-background border-border">
        <DialogHeader>
          <DialogTitle>Edit Display Preferences</DialogTitle>
          <DialogDescription>
            Change the order of fields and components by adjusting their display preferences.
            Lower numbers appear first.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[400px] overflow-y-auto">
          <table className="w-full border-collapse">
            <thead className="bg-muted/30 sticky top-0">
              <tr>
                <th className="text-left p-2 text-sm font-medium text-primary">Field Name</th>
                <th className="text-left p-2 text-sm font-medium text-primary">Type</th>
                <th className="text-center p-2 text-sm font-medium text-primary w-[120px]">Display Order</th>
              </tr>
            </thead>
            <tbody>
              {orderedFields.map((field, index) => (
                <tr
                  key={field.id}
                  className={`border-b border-border hover:bg-muted/20 ${field.isComponent ? 'bg-primary/5' : ''}`}
                >
                  <td className="p-2">
                    <div className="flex items-center">
                      {field.isComponent ? (
                        <Badge variant="outline" className="mr-2 bg-primary/20 text-primary border-primary/30">Component</Badge>
                      ) : (
                        <Badge variant="outline" className="mr-2 bg-muted/20 text-muted-foreground border-muted/30">Field</Badge>
                      )}
                      {field.name}
                    </div>
                  </td>
                  <td className="p-2">{getFieldTypeDisplayName(field)}</td>
                  <td className="p-2">
                    <Input
                      type="number"
                      value={field.displayPreference}
                      onChange={(e) => updateDisplayPreference(index, e.target.value)}
                      className="h-8 w-20 mx-auto text-center"
                      min="10"
                      step="10"
                      placeholder={(index + 1) * 10 + ''}
                    />
                  </td>

                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving} className="border-border">
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
          <Button onClick={saveDisplayPreferences} disabled={isSaving} className="bg-primary hover:bg-primary/90">
            {isSaving ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
