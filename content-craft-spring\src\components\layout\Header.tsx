import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { LogOut, User, Database } from 'lucide-react';
import { useAuthStore, useCollectionStore, useContentEntryStore, useComponentStore, useMediaStore } from '@/lib/store';
import { authApi } from '@/lib/api';
import { toast } from 'sonner';
import { useTenant } from '@/components/tenant/TenantProvider';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

export function Header() {
  const { user, logout, clearAllData } = useAuthStore();
  const { reset: resetCollections } = useCollectionStore();
  const { reset: resetContentEntries } = useContentEntryStore();
  const { reset: resetComponents } = useComponentStore();
  const { reset: resetMedia } = useMediaStore();
  const { currentTenant, isDefaultTenant, clearTenant } = useTenant();
  const navigate = useNavigate();
  const location = useLocation();

  // Navigation items for page title
  const navItems = [
    {
      title: 'Dashboard',
      path: '/dashboard',
    },
    {
      title: 'Collections',
      path: '/content-types',
    },
    {
      title: 'Components',
      path: '/components',
    },
    {
      title: 'Media Library',
      path: '/media-library',
    },
    {
      title: 'Settings',
      path: '/settings',
    },
  ];

  // Handle logout
  const handleLogout = async () => {
    console.log('Starting comprehensive logout process');

    try {
      // Call the backend logout endpoint first
      console.log('Calling backend logout endpoint');
      await authApi.logout();
      console.log('Backend logout successful');
    } catch (error) {
      console.error('Backend logout failed, but continuing with client cleanup:', error);
    }

    try {
      // Clear all authentication and cached data
      console.log('Clearing all client-side data');
      clearAllData();

      // Reset all store states
      console.log('Resetting all stores');
      resetCollections();
      resetContentEntries();
      resetComponents();
      resetMedia();

      // Clear tenant context
      if (clearTenant) {
        console.log('Clearing tenant context');
        clearTenant();
      }

      // Additional cleanup - force clear any remaining items
      console.log('Performing additional cleanup');

      // Clear any axios default headers
      delete window.axios?.defaults?.headers?.common?.Authorization;

      // Clear any cached API responses
      if (window.caches) {
        window.caches.keys().then(names => {
          names.forEach(name => {
            window.caches.delete(name);
          });
        });
      }

      console.log('Logout cleanup completed successfully');

      // Show success message
      toast.success('You have been logged out successfully');

      // Small delay to ensure all cleanup is complete
      setTimeout(() => {
        // Force navigation to login page
        window.location.href = '/login';
      }, 100);

    } catch (error) {
      console.error('Error during logout cleanup:', error);

      // Fallback - force clear everything and redirect
      try {
        localStorage.clear();
        sessionStorage.clear();
      } catch (storageError) {
        console.error('Error clearing storage:', storageError);
      }

      toast.info('You have been logged out');
      window.location.href = '/login';
    }
  };

  return (
    <header className="sticky top-0 z-10 flex h-14 items-center justify-between bg-[#4F46E5] px-6 text-white">
      <div className="flex items-center">
        <SidebarTrigger className="text-white hover:bg-white/10 mr-2" />
        <span className="font-medium">
          {navItems.find((item) => location.pathname.includes(item.path))?.title || 'Dashboard'}
        </span>
      </div>
      <div className="flex items-center gap-4">
        <ThemeToggle />

        {/* Tenant information */}
        {!isDefaultTenant && currentTenant && (
          <div className="flex items-center bg-indigo-700 px-3 py-1 rounded-md">
            <Database className="mr-2 h-4 w-4" />
            <span className="font-medium text-sm">{currentTenant}</span>
          </div>
        )}

        {/* User information */}
        <div className="flex items-center">
          <User className="mr-2 h-5 w-5" />
          <span className="font-medium">{user?.username || 'User'}</span>
        </div>

        {/* Logout button */}
        <button
          onClick={handleLogout}
          className="flex items-center gap-2 rounded-md bg-white px-4 py-1.5 text-[#4F46E5] font-medium hover:bg-opacity-90 transition-colors"
        >
          <LogOut className="h-4 w-4" />
          <span>Logout</span>
        </button>
      </div>
    </header>
  );
}
