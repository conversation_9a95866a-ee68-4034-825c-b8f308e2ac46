import { createContext, useContext, useEffect, useState } from "react";
import { getTenantFromToken } from "@/lib/jwt";

type TenantContextType = {
  currentTenant: string | null;
  setCurrentTenant: (tenant: string | null) => void;
  clearTenant: () => void;
  isDefaultTenant: boolean;
};

const initialState: TenantContextType = {
  currentTenant: null,
  setCurrentTenant: () => null,
  clearTenant: () => null,
  isDefaultTenant: true,
};

const TenantContext = createContext<TenantContextType>(initialState);

type TenantProviderProps = {
  children: React.ReactNode;
};

export function TenantProvider({ children }: TenantProviderProps) {
  const [currentTenant, setCurrentTenant] = useState<string | null>(null);

  // Initialize tenant from JWT token on mount
  useEffect(() => {
    const token = localStorage.getItem('cms_token');
    if (token) {
      const tenant = getTenantFromToken(token);
      console.log('Initializing tenant context from token:', tenant);
      setCurrentTenant(tenant);
    }
  }, []);

  // Clear tenant function
  const clearTenant = () => {
    console.log('Clearing tenant context');
    setCurrentTenant(null);
  };

  // Determine if we're using the default tenant
  const isDefaultTenant = !currentTenant || currentTenant === 'public';

  const value = {
    currentTenant,
    setCurrentTenant,
    clearTenant,
    isDefaultTenant,
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export const useTenant = () => {
  const context = useContext(TenantContext);

  if (context === undefined) {
    throw new Error("useTenant must be used within a TenantProvider");
  }

  return context;
};
