import axios from "axios";
import { getTenantFromToken } from "./jwt";

// Determine the API base URL
export const getBaseUrl = () => {
  // Check for URL parameters that might override the API URL (highest priority)
  const urlParams = new URLSearchParams(window.location.search);
  const apiUrlParam = urlParams.get("apiUrl");
  if (apiUrlParam) {
    console.log("Using API URL from URL parameter:", apiUrlParam);
    return apiUrlParam;
  }

  // Use environment variable from .env files (second priority)
  if (import.meta.env.VITE_API_URL) {
    // If we're in development mode, log the source
    if (import.meta.env.VITE_DEBUG === "true") {
      console.log(
        "Using API URL from environment:",
        import.meta.env.VITE_API_URL
      );
    }
    return import.meta.env.VITE_API_URL;
  }

  // Dynamic detection based on current hostname (third priority)
  const isLocalhost =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1";

  // Check if accessing via IP address (for development on other devices)
  const isLocalNetwork =
    /^192\.168\.\d+\.\d+$/.test(window.location.hostname) ||
    /^10\.\d+\.\d+\.\d+$/.test(window.location.hostname) ||
    /^172\.(1[6-9]|2\d|3[0-1])\.\d+\.\d+$/.test(window.location.hostname);

  let baseUrl;
  // If running locally or on local network, use the backend server's address
  // Otherwise, assume API is available at the same domain but with /api path
  if (isLocalhost) {
    // Try multiple ports in case the default port is not working
    // First try the default port 8071, then try 8080 which is a common Spring Boot port
    try {
      // Check if we can connect to port 8071
      const testUrl = "http://localhost:8071/api";
      console.log("Testing API connection to:", testUrl);

      // For now, default to 8080 since we know 8071 isn't working based on earlier tests
      baseUrl = "http://localhost:8080";
      console.log("Using API URL:", baseUrl);
    } catch (error) {
      console.log("Error connecting to port 8071, falling back to port 8080");
      baseUrl = "http://localhost:8080";
    }
  } else if (isLocalNetwork) {
    // When accessing via IP, use the same IP for the backend
    // Try both ports 8071 and 8080
    baseUrl = `http://${window.location.hostname}:8080`;
    console.log("Using network API URL:", baseUrl);
  } else {
    baseUrl = `${window.location.origin}/api`;
  }

  // Log the base URL for debugging
  if (import.meta.env.VITE_DEBUG === "true") {
    console.log("API Base URL (dynamically determined):", baseUrl);
  }
  return baseUrl;
};

// Create axios instance with default config
const api = axios.create({
  baseURL: getBaseUrl(),
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "Cache-Control": "no-cache",
  },
  // Disable withCredentials since we're using JWT tokens in Authorization header
  // This avoids CORS preflight issues with credentials
  withCredentials: false,
  // Add timeout to prevent hanging requests
  timeout: 10000,
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(
      `API Request: ${config.method?.toUpperCase()} ${config.baseURL}${
        config.url
      }`,
      {
        params: config.params,
        data: config.data,
        headers: config.headers,
      }
    );
    return config;
  },
  (error) => {
    console.error("API Request Error:", error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`, {
      data: response.data,
      headers: response.headers,
    });
    return response;
  },
  (error) => {
    console.error(`API Error: ${error.config?.url}`, {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      request: {
        method: error.config?.method,
        params: error.config?.params,
        data: error.config?.data,
      },
    });
    return Promise.reject(error);
  }
);

// Add interceptor to attach JWT token and tenant information to requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("cms_token");
    console.log("API Request to:", config.url);
    console.log("Token available for request:", token ? "Yes" : "No");

    if (token) {
      // Add Authorization header with JWT token
      config.headers.Authorization = `Bearer ${token}`;
      console.log(
        "Authorization header set:",
        `Bearer ${token.substring(0, 15)}...`
      );

      // Extract tenant from token and add X-TenantID header
      const tenant = getTenantFromToken(token);
      if (tenant && tenant !== "public") {
        config.headers["X-TenantID"] = tenant;
        console.log("X-TenantID header set:", tenant);
      } else {
        console.log("No tenant found in token or using default tenant");
      }
    } else {
      console.log("No token available, request will be unauthenticated");
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle authentication errors
api.interceptors.response.use(
  (response) => {
    console.log("API Response success from:", response.config.url);
    return response;
  },
  (error) => {
    console.log("API Response error from:", error.config?.url);
    console.error("Error details:", error.message);

    // Log response details if available
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }

    // Handle 401 Unauthorized errors
    if (error.response && error.response.status === 401) {
      console.error("Authentication error - 401 received");

      // Don't auto-logout immediately after login - give some time for the session to establish
      const token = localStorage.getItem("cms_token");
      if (token) {
        try {
          // Check if token was just created (within last 30 seconds)
          const payload = JSON.parse(atob(token.split('.')[1]));
          const tokenAge = Date.now() - (payload.iat * 1000);

          if (tokenAge < 30000) { // Less than 30 seconds old
            console.log("Token is very new, not auto-logging out due to 401");
            return Promise.reject(error);
          }
        } catch (e) {
          console.warn("Could not parse token age:", e);
        }
      }

      console.error("Authentication error - clearing token");

      // Clear token from localStorage
      localStorage.removeItem("cms_token");

      // If not on login page, redirect to login
      if (window.location.pathname !== "/login") {
        console.log("Redirecting to login due to authentication error");
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

// API services
export const authApi = {
  login: (username: string, password: string) =>
    api.post("/auth/login", { username, password }),

  register: (username: string, email: string, password: string) =>
    api.post("/auth/register", { username, email, password }),

  logout: () => api.post("/auth/logout"),
};

export const collectionsApi = {
  getAll: () => api.get("/collections/getAll"),
  getDetails: () => api.get("/collections/getAllWithDetails"),
  getById: (id: string) => api.get(`/collections/getById/${id}`),
  getByIdWithDetails: (id: string) =>
    api.get(`/collections/getByIdWithDetails/${id}`),
  getByApiId: (apiId: string) => api.get(`/collections/getByApiId/${apiId}`),
  create: (data: any) => api.post("/collections/create", data),
  update: (id: string, data: any) => api.put(`/collections/update/${id}`, data),
  delete: (id: string) => api.delete(`/collections/deleteById/${id}`),
  getByCategoryId: (categoryId: string) =>
    api.get(`/collections/category/${categoryId}`),


};


export const clientsApi = {
  getAll: () => api.get('/clients/getAll'),
  create: (data: { name: string }) => api.post('/clients/create', data),
  getById: (id: string) => api.get(`/clients/${id}`),
  update: (id: string, data: { name: string }) => api.put(`/clients/update/${id}`, data),
  delete: (id: string) => api.delete(`/clients/${id}`)
};


export const componentsApi = {
  getAll: () => api.get("/components/getAll"),
  getActive: () => api.get("/components/getAllActive"),
  getById: (id: string) => api.get(`/components/getById/${id}`),
  getByIdWithDetails: (id: string) =>
    api.get(`/components/getByIdWithDetails/${id}`),
  getByApiId: (apiId: string) => api.get(`/components/getByApiId/${apiId}`),
  create: (data: any) => api.post("/components/create", data),
  update: (id: string, data: any) => api.put(`/components/update/${id}`, data),
  delete: (id: string) => api.delete(`/components/deleteById/${id}`),
};

export const componentFieldsApi = {
  getAll: () => api.get("/component-fields/getAll"),
  getById: (id: string) => api.get(`/component-fields/getById/${id}`),
  getByComponentId: (componentId: string, config?: any) =>
    api.get(`/component-fields/getByComponentId/${componentId}`, config),
  create: (data: any) => api.post("/component-fields/create", data),
  update: (id: string, data: any) =>
    api.put(`/component-fields/update/${id}`, data),
  delete: (id: string) => api.delete(`/component-fields/deleteById/${id}`),
  getNextId: () => api.get("/component-fields/getNextId"),
};

export const componentFieldConfigsApi = {
  getByComponentFieldId: (componentFieldId: string) =>
    api.get(`/component-field-configs/getByFieldId/${componentFieldId}`),
  create: (data: any) => api.post("/component-field-configs/create", data),
  update: (id: string, data: any) =>
    api.put(`/component-field-configs/update/${id}`, data),
  delete: (id: string) =>
    api.delete(`/component-field-configs/deleteById/${id}`),
  bulkCreate: (data: any[]) =>
    api.post("/component-field-configs/createBulk", data),
};

export const fieldTypesApi = {
  getAll: () => api.get("/field-types/getAll"),
  getActive: () => api.get("/field-types/getAllActive"),
  getById: (id: string) => api.get(`/field-types/getById/${id}`),
  getActiveWithRetry: () =>
    api.get("/field-types/getAllActive", {
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "Cache-Control": "no-cache",
      },
    }),
};

export const fieldConfigsApi = {
  getAll: () => api.get("/field-configs/getAll"),
  getActive: () => api.get("/field-configs/getAllActive"),
  getByFieldType: (fieldTypeId: string) =>
    api.get(`/field-configs/getByFieldTypeId/${fieldTypeId}`),
  getById: (id: string) => api.get(`/field-configs/getById/${id}`),
  create: (data: any) => api.post("/field-configs/create", data),
  update: (id: string, data: any) =>
    api.put(`/field-configs/update/${id}`, data),
};


export const parentCategoriesApi = {
  // getAll: () => api.get("/parent-categories/getAll"),
  getByClientId: (id: string) => api.get(`/categories/client/${id}`),
  getById: (id: string) => api.get(`/parent-categories/getById/${id}`),
  create: (data: any) => api.post("/categories/create", data),
  update: (id: string, data: any) =>
    api.put(`/categories/update/${id}`, data),
  delete: (id: string) => api.delete(`/categories/deleteById/${id}`),
}

export const collectionFieldsApi = {
  getAll: () => api.get("/collection-fields/getAll"),
  getById: (id: string) => api.get(`/collection-fields/getById/${id}`),
  getByCollectionId: (collectionId: string) =>
    api.get(`/collection-fields/getByCollectionId/${collectionId}`),
  create: (data: any) => api.post("/collection-fields/create", data),
  update: (id: string, data: any) =>
    api.put(`/collection-fields/update/${id}`, data),
  delete: (id: string) => api.delete(`/collection-fields/deleteById/${id}`),
  getNextId: () => api.get("/collection-fields/getNextId"),
};

export const collectionFieldConfigsApi = {
  getByCollectionFieldId: (collectionFieldId: string) =>
    api.get(`/collection-field-configs/getByFieldId/${collectionFieldId}`),
  create: (data: any) => api.post("/collection-field-configs/create", data),
  update: (id: string, data: any) =>
    api.put(`/collection-field-configs/update/${id}`, data),
  delete: (id: string) =>
    api.delete(`/collection-field-configs/deleteById/${id}`),
  bulkCreate: (data: any[]) =>
    api.post("/collection-field-configs/createBulk", data),
};

export const collectionComponentsApi = {
  getAll: () => api.get("/collection-components/getAll"),
  getById: (id: string) => api.get(`/collection-components/getById/${id}`),
  getByCollectionId: (collectionId: string) =>
    api.get(`/collection-components/getByCollectionId/${collectionId}`),
  create: (data: any) => api.post("/collection-components/create", data),
  update: (id: string, data: any) =>
    api.put(`/collection-components/update/${id}`, data),
  delete: (id: string) => api.delete(`/collection-components/deleteById/${id}`),
  getNextId: () => api.get("/collection-components/getNextId"),
};

export const componentComponentsApi = {
  getAll: () => api.get("/component-components/getAll"),
  getById: (id: string) => api.get(`/component-components/getById/${id}`),
  getByParentId: (parentId: string) =>
    api.get(`/component-components/getByParentId/${parentId}`),
  create: (data: any) => api.post("/component-components/create", data),
  update: (id: string, data: any) =>
    api.put(`/component-components/update/${id}`, data),
  delete: (id: string) => api.delete(`/component-components/delete/${id}`),
  reorder: (parentId: string, componentIds: number[]) =>
    api.post(`/component-components/reorder/${parentId}`, componentIds),
};

export const collectionOrderingApi = {
  getOrderedItems: (collectionId: string) =>
    api.get(`/collections/${collectionId}/ordering`),
  reorderItems: (
    collectionId: string,
    data: { componentIds: number[]; fieldIds: number[] }
  ) => api.put(`/collections/${collectionId}/ordering`, data),
};

export const contentEntriesApi = {
  getAll: () => api.get("/content-entries/getAll"),
  getById: (id: string) => api.get(`/content-entries/getById/${id}`),
  getByCollection: (collectionId: string) =>
    api.get(`/content-entries/getByCollectionId/${collectionId}`),
  create: (data: any) => api.post("/content-entries/create", data),
  update: (id: string, data: any) =>
    api.put(`/content-entries/update/${id}`, data),
  delete: (id: string) => api.delete(`/content-entries/deleteById/${id}`),
};

export const categoriesApi = {
  getAll: () => api.get("/categories/getAll"),
  getById: (id: string) => api.get(`/categories/getById/${id}`),
  getByName: (name: string) => api.get(`/categories/getByName/${name}`),
  create: (data: any) => api.post("/categories/create", data),
  update: (id: string, data: any) => api.put(`/categories/update/${id}`, data),
  delete: (id: string) => api.delete(`/categories/deleteById/${id}`),

   getByClientId: (id: string) => api.get(`/categories/client/${id}`),
   getByParentCategoryId: (id: string) => api.get(`/categories/getByParentId/${id}`),
   getChildCategoriesByParentAndClient: (parentId: string, clientId: string) =>
     api.get(`/categories/getByParentId/${parentId}/client/${clientId}`),

};

export const publicApi = {
  getCollections: () => api.get("/public/collections/getAll"),
  getSimplifiedCollections: () =>
    api.get("/public/simplified-collections/getAll"),
};

export const simplifiedCollectionsApi = {
  getAll: () => api.get("/simplified-collections/getAll"),
  getByApiId: (apiId: string) =>
    api.get(`/simplified-collections/getByApiId/${apiId}`),
};

// Media API endpoints
export const mediaApi = {
  // Assets
  getAllAssets: () => api.get("/media/assets/getAll"),
  // Fallback method in case the backend uses a different endpoint
  _getAllAssets: () => api.get("/media/assets"),
  getAssetById: (id: string) => api.get(`/media/assets/getById/${id}`),
  getAssetsByFolder: (folderId: string) =>
    api.get(`/media/assets/getByFolderId/${folderId}`),
  // Fallback method in case the backend uses a different endpoint
  _getAssetsByFolder: (folderId: string) =>
    api.get(`/media/assets/folder/${folderId}`),
  uploadAsset: (formData: FormData) =>
    api.post("/media/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  // Fallback method in case the backend uses a different endpoint
  _uploadAsset: (formData: FormData) =>
    api.post("/media/assets/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  updateAsset: (id: string, data: any) =>
    api.put(`/media/assets/update/${id}`, data),
  // Alternative method for renaming assets
  renameAsset: (id: string, newFileName: string) =>
    api.put(`/media/assets/rename/${id}`, { fileName: newFileName }),
  // Fallback method for renaming assets
  _renameAsset: (id: string, newFileName: string) =>
    api.put(`/media/assets/${id}/rename`, { fileName: newFileName }),
  deleteAsset: (id: string) => api.delete(`/media/assets/deleteById/${id}`),
  searchAssets: (query: string) =>
    api.get(`/media/assets/search?query=${encodeURIComponent(query)}`),
  generateShareLink: (id: string) => api.post(`/media/assets/share/${id}`),
  replaceFile: (id: string, formData: FormData) =>
    api.post(`/media/assets/replace/${id}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),
  // New endpoints for direct file access and download
  getAssetContent: (id: string) =>
    api.get(`/media/assets/${id}/content`, {
      responseType: "blob",
    }),
  downloadAsset: (id: string) =>
    api.get(`/media/assets/${id}/download`, {
      responseType: "blob",
    }),
  // Direct file download by path
  downloadFile: (year: string, month: string, fileName: string) =>
    api.get(`/media/download/${year}/${month}/${fileName}`, {
      responseType: "blob",
    }),

  // Folders
  getAllFolders: () => api.get("/media/folders/getAll"),
  // Fallback method in case the backend uses a different endpoint
  _getAllFolders: () => api.get("/media/folders"),
  getFolderById: (id: string) => api.get(`/media/folders/getById/${id}`),
  getSubfolders: (parentId: number) =>
    api.get(`/media/folders/getSubfolders/${parentId}`),
  // Fallback method in case the backend uses a different endpoint
  _getSubfolders: (parentId: number) =>
    api.get(`/media/folders/getByParent/${parentId}`),
  createFolder: (data: {
    folderName: string;
    description?: string;
    parentId?: number;
  }) => api.post("/media/folders/create", null, { params: data }),
  // Fallback method in case the backend uses a different endpoint
  _createFolder: (data: {
    folderName: string;
    description?: string;
    parentId?: number;
  }) => api.post("/media/folders/create", data),
  updateFolder: (id: string, data: any) =>
    api.put(`/media/folders/update/${id}`, null, { params: data }),
  deleteFolder: (id: string) => api.delete(`/media/folders/deleteById/${id}`),
  searchFolders: (query: string) =>
    api.get(`/media/folders/search?query=${encodeURIComponent(query)}`),
};

// API Tokens API
export const apiTokensApi = {
  getAll: () => api.get("/api-tokens"),
  create: (name: string, description: string, expirationDays: number) =>
    api.post("/api-tokens", { name, description, expirationDays }),
  revoke: (id: number) => api.delete(`/api-tokens/${id}`),
};

export default api;
