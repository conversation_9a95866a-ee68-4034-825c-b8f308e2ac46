import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, ArrowLeft, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { categoriesApi, clientsApi, collectionsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';

interface Client {
  id: number;
  name: string;
}

interface Collection {
  id: string;
  collectionName: string;
  apiId: string;
  fields: any[];
}

export default function ClientCollections() {
  const navigate = useNavigate();
  const { clientId } = useParams<{ clientId: string }>();
  const { toast } = useToast();
  
  const [client, setClient] = useState<Client | null>(null);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [filteredCollections, setFilteredCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [createCollectionDialogOpen, setCreateCollectionDialogOpen] = useState(false);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      if (!clientId) return;
      
      setLoading(true);
      try {
        // Fetch client details
        const clientResponse = await clientsApi.getById(clientId);
        setClient(clientResponse.data);
        
        // Fetch collections for this client
        // Note: This is a placeholder. You'll need to implement the actual API endpoint
        // that filters collections by clientId
        const collectionsResponse = await categoriesApi.getByClientId(clientId);
        
        // Filter collections by clientId (this would ideally be done on the server)
        // This is just a placeholder implementation
        const clientCollections = collectionsResponse.data.filter(
          (collection: any) => collection.clientId === clientId
        );
        
        setCollections(clientCollections);
        setFilteredCollections(clientCollections);
        setTotalPages(Math.ceil(clientCollections.length / itemsPerPage));
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load client collections',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [clientId, toast, itemsPerPage]);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCollections(collections);
    } else {
      const filtered = collections.filter(collection => 
        collection.collectionName.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCollections(filtered);
    }
    setCurrentPage(1);
    setTotalPages(Math.ceil(filteredCollections.length / itemsPerPage));
  }, [searchQuery, collections]);

  const handleEditCollection = (collectionId: string) => {
    navigate(`/content-types/edit/${collectionId}`);
  };

  const handleCreateCollection = () => {
    setCreateCollectionDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    setCreateCollectionDialogOpen(false);
  };

  const handleSaveCollection = async (collectionData: any) => {
    try {
      // Add clientId to the collection data
      collectionData.clientId = clientId;
      
      const response = await collectionsApi.create(collectionData);
      console.log('Collection created successfully:', response.data);

      toast({
        title: 'Collection created',
        description: 'Your collection has been created successfully',
      });

      // Close the dialog
      setCreateCollectionDialogOpen(false);

      // Navigate to the edit page
      navigate(`/content-types/edit/${response.data.id}`);
    } catch (apiError: any) {
      console.error('API error creating collection:', apiError);
      toast({
        title: 'Error',
        description: 'Failed to create collection',
        variant: 'destructive',
      });
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCollections.slice(startIndex, endIndex);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/clients')}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>

            <h1 className="text-3xl font-bold">
              {loading ? 'Loading...' : `Collections for ${client?.name || 'Client'}`}
            </h1>
          </div>
          <div>
            <Button onClick={handleCreateCollection}>
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search collections..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : filteredCollections.length === 0 ? (
        <div className="bg-muted/50 rounded-md p-6 text-center">
          <h3 className="text-lg font-medium mb-2">No collections found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {searchQuery ? 
              `No collections match your search query "${searchQuery}" for this client.` : 
              'This client has no collections yet.'}
          </p>
          {searchQuery ? (
            <Button variant="outline" onClick={() => setSearchQuery('')}>
              Clear search
            </Button>
          ) : (
            <Button onClick={handleCreateCollection}>
              <Plus className="mr-2 h-4 w-4" />
              Create Collection
            </Button>
          )}
        </div>
      ) : (
        <>
        <div className="border rounded-md overflow-hidden">
          {getCurrentPageItems().map((collection, index) => (
            <div
              key={collection.id}
              className={`p-4 ${index !== collections.length - 1 ? 'border-b' : ''} hover:bg-muted/50 cursor-pointer`}
              onClick={() => handleEditCollection(collection.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 border border-border">
                    {(collection.collectionName || 'U')[0].toUpperCase()}
                  </div>
                  <div>
                    <h3 className="font-medium">{collection.collectionName}</h3>
                    <p className="text-xs text-muted-foreground">API ID: {collection.apiId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-full">
                    {collection.fields?.length || 0} fields
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </span>
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
        </>
      )}

      {/* Create collection dialog */}
<BasicCollectionDialog
  isOpen={createCollectionDialogOpen}
  onClose={handleCloseCollectionDialog}
  onSave={handleSaveCollection}
  clientId={clientId}
/>
    </div>
  );
}

