import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, ArrowUp, Search, Building, Edit, ArrowLeft } from 'lucide-react';
import { clientsApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useClickedClient } from '@/lib/store';

interface Client {
  id: number;
  name: string;
  createdBy: string;
  createdAt: string;
  modifiedBy: string;
  modifiedAt: string;
}

export default function Clients() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [createClientDialogOpen, setCreateClientDialogOpen] = useState(false);
  const [editClientDialogOpen, setEditClientDialogOpen] = useState(false);
  const [clientName, setClientName] = useState('');
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [editClientName, setEditClientName] = useState('');
  const [showScrollButton, setShowScrollButton] = useState(false);

  const { clientId, setClientId } = useClickedClient();

  // Fetch clients on component mount
  useEffect(() => {
    fetchClients();

    // Add scroll listener
    const handleScroll = () => {
      setShowScrollButton(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const response = await clientsApi.getAll();
      setClients(response.data || []);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: 'Error',
        description: 'Failed to load clients',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClient = async () => {
    if (!clientName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Client name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      const clientData = {
        name: clientName.trim()
      };

      const response = await clientsApi.create(clientData);
      console.log('Client created successfully:', response.data);

      toast({
        title: 'Client created',
        description: `Client "${clientName}" has been created successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setCreateClientDialogOpen(false);
      setClientName('');
    } catch (error: any) {
      console.error('Error creating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to create client: ' + error.message,
        variant: 'destructive',
      });
    }
  };

  // Handle edit client
  const handleEditClient = (client: Client) => {
    setEditingClient(client);
    setEditClientName(client.name);
    setEditClientDialogOpen(true);
  };

  // Handle update client
  const handleUpdateClient = async () => {
    if (!editClientName.trim() || !editingClient) {
      toast({
        title: 'Error',
        description: 'Client name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      const clientData = {
        name: editClientName.trim()
      };

      await clientsApi.update(editingClient.id.toString(), clientData);

      toast({
        title: 'Client updated',
        description: `Client "${editClientName}" has been updated successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setEditClientDialogOpen(false);
      setEditingClient(null);
      setEditClientName('');
    } catch (error: any) {
      console.error('Error updating client:', error);
      toast({
        title: 'Error',
        description: 'Failed to update client: ' + (error.response?.data?.message || error.message),
        variant: 'destructive',
      });
    }
  };

  // Filter clients based on search query
  const getFilteredClients = () => {
    if (!searchQuery.trim()) return clients;

    return clients.filter(client =>
      client.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/dashboard')}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Clients</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setCreateClientDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Client
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search clients..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : getFilteredClients().length === 0 ? (
          <div className="bg-muted/50 rounded-md p-6 text-center">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No clients found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery ? 'No clients match your search query' : 'Create your first client to get started'}
            </p>
            <Button onClick={() => setCreateClientDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Client
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {getFilteredClients().map((client) => (
              <div
                key={client.id}
                className="bg-card border rounded-md shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                onClick={
                  () =>{
                    navigate(`/parent-categories`)
                    useClickedClient.getState().setClientId(client.id);
                  }
              }
              >
                <div className="p-4 border-b">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3">
                      <Building className="h-4 w-4" />
                    </div>
                    <h3 className="font-medium">{client.name}</h3>
                  </div>
                </div>
                <div className="p-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Created {new Date(client.createdAt).toLocaleDateString()}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditClient(client);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create client dialog */}
      <Dialog open={createClientDialogOpen} onOpenChange={setCreateClientDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                C
              </div>
              <DialogTitle>Create a client</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Client</h3>
              <p className="text-sm text-muted-foreground">Create a client to organize your collections</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="clientName" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="clientName"
                    placeholder="Enter client name"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setCreateClientDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateClient}>
              Create Client
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit client dialog */}
      <Dialog open={editClientDialogOpen} onOpenChange={setEditClientDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                <Edit className="h-4 w-4" />
              </div>
              <DialogTitle>Edit Client</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Rename Client</h3>
              <p className="text-sm text-muted-foreground">Update the client name</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="editClientName" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="editClientName"
                    placeholder="Enter client name"
                    value={editClientName}
                    onChange={(e) => setEditClientName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateClient();
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setEditClientDialogOpen(false);
                setEditingClient(null);
                setEditClientName('');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateClient}>
              Update Client
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary hover:bg-primary/90 transition-all"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}
    </div>
  );
}