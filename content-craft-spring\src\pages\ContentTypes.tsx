import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Database, LayoutGrid, Trash2, Edit, ArrowUp, Search, Folder, MoreHorizontal, Pencil } from 'lucide-react';
import { useCollectionStore } from '@/lib/store';
import { collectionsApi, collectionFieldsApi, categoriesApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import BasicCollectionDialog from '@/components/content-type/BasicCollectionDialog';
import { RenameCategoryDialog } from '@/components/dialogs/RenameCategoryDialog';
import { DeleteCategoryDialog } from '@/components/dialogs/DeleteCategoryDialog';

export default function ContentTypes() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const {
    collections,
    setCollections,
    setSelectedCollection,
    removeCollection,
    addCollection,
    setLoading,
    loading
  } = useCollectionStore();

  const [deleteConfirmOpen, setDeleteConfirmOpen] = React.useState(false);
  const [collectionToDelete, setCollectionToDelete] = React.useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = React.useState(false);
  const [createCategoryDialogOpen, setCreateCategoryDialogOpen] = React.useState(false);
  const [categoryName, setCategoryName] = React.useState('');
  const [categoryDescription, setCategoryDescription] = React.useState('');
  const [renameCategoryDialogOpen, setRenameCategoryDialogOpen] = React.useState(false);
  const [deleteCategoryDialogOpen, setDeleteCategoryDialogOpen] = React.useState(false);
  const [selectedCategory, setSelectedCategory] = React.useState<any>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = React.useState(1);
  const [itemsPerPage, setItemsPerPage] = React.useState(5);
  const [totalPages, setTotalPages] = React.useState(1);

  // Scroll to top button state
  const [showScrollButton, setShowScrollButton] = React.useState(false);

  // Search and filter state
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedCategoryId, setSelectedCategoryId] = React.useState<string | null>(null);

  // Categories state
  const [categories, setCategories] = React.useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = React.useState(false);

  // Function to check database connection
  const checkDatabaseConnection = async () => {
    try {
      await collectionsApi.getAll();
      console.log('Database connection successful');
      return true;
    } catch (error: any) {
      console.error('Database connection error:', error);

      let errorMessage = 'Failed to connect to the database';
      if (error.response) {
        errorMessage = `Server error: ${error.response.status}`;
      } else if (error.request) {
        errorMessage = 'No response from server. Please check if the backend is running.';
      } else {
        errorMessage = `Error: ${error.message}`;
      }

      toast({
        title: 'Database Connection Error',
        description: errorMessage,
        variant: 'destructive',
      });

      return false;
    }
  };

  React.useEffect(() => {
    const fetchCollections = async () => {
      setLoading(true);
      try {
        // First check database connection
        const isConnected = await checkDatabaseConnection();
        if (!isConnected) {
          return;
        }

        // Fetch categories
        try {
          setLoadingCategories(true);
          const categoriesResponse = await categoriesApi.getAll();
          console.log('Categories fetched from API:', categoriesResponse.data);
          setCategories(categoriesResponse.data || []);
        } catch (categoriesError) {
          console.error('Error fetching categories:', categoriesError);
          // Show a toast notification for the categories error
          toast({
            title: 'Categories Error',
            description: 'Could not load categories. Using default categories instead.',
            variant: 'destructive',
          });

          // Set default categories if API call fails
          setCategories([
            { id: 1, categoryName: 'CORP' },
            { id: 2, categoryName: 'LLC' },
            { id: 3, categoryName: 'Other' }
          ]);
        } finally {
          setLoadingCategories(false);
        }

        const response = await collectionsApi.getAll();
        console.log('Collections fetched from API:', response.data);

        // Transform the backend data format to the frontend format
        const formattedCollections = [];

        for (const collection of response.data) {
          let apiIdPlural = '';
          let draftAndPublish = false;
          let isInternationally = false;

          // Try to parse additional information if available
          if (collection.additionalInformation) {
            try {
              const additionalInfo = JSON.parse(collection.additionalInformation);
              apiIdPlural = additionalInfo.apiIdPlural || '';
              draftAndPublish = additionalInfo.draftAndPublish || false;
              isInternationally = additionalInfo.isInternationally || false;
            } catch (parseError) {
              console.error('Error parsing additional information:', parseError);
            }
          }

          // Create the formatted collection object
          const formattedCollection = {
            id: collection.id.toString(),
            name: collection.collectionName || 'Unnamed Collection',
            apiId: collection.collectionApiId || '',
            apiIdPlural,
            draftAndPublish,
            isInternationally,
            fields: [],
            isActive: true,
            createdAt: '',
            updatedAt: '',
            category: collection.category,
            categoryName: collection.categoryName
          };

          // Fetch fields for this collection
          try {
            console.log(`Fetching fields for collection ID: ${collection.id}`);
            const fieldsResponse = await collectionFieldsApi.getByCollectionId(collection.id.toString());
            console.log(`Fields for collection ${collection.id}:`, fieldsResponse.data);

            if (fieldsResponse.data && Array.isArray(fieldsResponse.data)) {
              formattedCollection.fields = fieldsResponse.data.map((field: any) => {
                let fieldName = '';
                let apiId = '';
                let required = false;
                let unique = false;

                // Try to parse additionalInformation to get field details
                if (field.additionalInformation) {
                  try {
                    const additionalInfo = JSON.parse(field.additionalInformation);
                    fieldName = additionalInfo.name || '';
                    apiId = additionalInfo.apiId || '';
                    required = additionalInfo.required || false;
                    unique = additionalInfo.unique || false;
                  } catch (error) {
                    console.error('Error parsing additionalInformation for field:', field.id, error);
                  }
                }

                // If no name found in additionalInformation, use field type as fallback
                if (!fieldName && field.fieldType) {
                  if (field.fieldType.displayName) {
                    fieldName = field.fieldType.displayName;
                  } else if (field.fieldType.fieldTypeName) {
                    // Format the field type name to be more readable
                    fieldName = field.fieldType.fieldTypeName
                      .split('_')
                      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                      .join(' ');
                  }
                }

                // Last resort fallback
                if (!fieldName) {
                  fieldName = `Field ${field.id}`;
                }

                return {
                  id: field.id.toString(),
                  name: fieldName,
                  apiId: apiId,
                  type: field.fieldType ? field.fieldType.id : 0,
                  required: required,
                  unique: unique
                };
              });
            }
          } catch (fieldsError) {
            console.error(`Error fetching fields for collection ${collection.id}:`, fieldsError);
          }

          formattedCollections.push(formattedCollection);
        }

        console.log('Formatted collections with fields:', formattedCollections);
        setCollections(formattedCollections);
      } catch (error) {
        console.error('Error fetching collections:', error);
        toast({
          title: 'Error',
          description: 'Failed to load content types',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCollections();
  }, [setCollections, setLoading, toast]);

  // Update total pages when collections, search query, or itemsPerPage changes
  React.useEffect(() => {
    const filteredCollections = getFilteredCollections();
    if (filteredCollections.length > 0) {
      const newTotalPages = Math.max(1, Math.ceil(filteredCollections.length / itemsPerPage));
      setTotalPages(newTotalPages);

      // Reset to page 1 if current page is now invalid
      if (currentPage > newTotalPages) {
        setCurrentPage(1);
      }
    } else {
      setTotalPages(1);
    }
  }, [collections.length, itemsPerPage, currentPage, searchQuery]);

  // Handle scroll event to show/hide scroll-to-top button
  React.useEffect(() => {
    const handleScroll = () => {
      // Show button when user scrolls down 300px
      if (window.scrollY > 300) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Clean up the event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Safe function to get category ID from a collection
  const getCategoryId = (collection) => {
    try {
      if (!collection) return null;
      if (!collection.category) return null;

      if (typeof collection.category === 'object' && collection.category !== null) {
        return collection.category.id;
      } else if (typeof collection.category === 'number') {
        return collection.category;
      } else if (typeof collection.category === 'string') {
        return parseInt(collection.category);
      }
      return null;
    } catch (error) {
      console.error('Error in getCategoryId:', error);
      return null;
    }
  };

  // Filter collections based on search query and selected category
  const getFilteredCollections = () => {
    let filtered = collections;

    // Filter by category if one is selected
    if (selectedCategoryId) {
      console.log(`Filtering by category ID: ${selectedCategoryId}`);

      // Convert selectedCategoryId to number for comparison
      const categoryIdNum = parseInt(selectedCategoryId);

      filtered = filtered.filter(collection => {
        try {
          // Use our safe getCategoryId function
          const categoryId = getCategoryId(collection);
          const matches = categoryId === categoryIdNum;

          if (matches) {
            console.log(`Collection ${collection.id} matches category ${selectedCategoryId}`);
          }

          return matches;
        } catch (error) {
          console.error(`Error filtering collection ${collection?.id}:`, error);
          return false;
        }
      });

      console.log(`Found ${filtered.length} collections in category ${selectedCategoryId}`);
    }

    // Then filter by search query if one exists
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(collection =>
        collection.name.toLowerCase().includes(query) ||
        collection.apiId.toLowerCase().includes(query)
      );
    }

    return filtered;
  };

  // Get paginated collections
  const getPaginatedCollections = () => {
    const filteredCollections = getFilteredCollections();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCollections.slice(startIndex, endIndex);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string) => {
    try {
      if (selectedCategoryId === categoryId) {
        // If clicking the same category, clear the filter
        setSelectedCategoryId(null);
        console.log('Cleared category filter');
      } else {
        // Otherwise, set the selected category
        setSelectedCategoryId(categoryId);
        console.log(`Filtering collections by category ID: ${categoryId}`);

        // Log collections in this category
        const categoryIdNum = parseInt(categoryId);
        const categoryCollections = collections.filter(collection => {
          try {
            if (!collection || !collection.category) return false;

            // Handle different category formats
            let collectionCategoryId = null;
            if (typeof collection.category === 'object' && collection.category !== null) {
              collectionCategoryId = collection.category.id;
            } else if (typeof collection.category === 'number') {
              collectionCategoryId = collection.category;
            } else if (typeof collection.category === 'string') {
              collectionCategoryId = parseInt(collection.category);
            }

            const matches = collectionCategoryId === categoryIdNum;
            if (matches) {
              console.log(`Collection ${collection.id} (${collection.name}) matches category ${categoryId}`);
            }
            return matches;
          } catch (e) {
            console.error('Error filtering collection:', e);
            return false;
          }
        });

        console.log(`Found ${categoryCollections.length} collections in category ${categoryId}:`, categoryCollections);
      }
      setCurrentPage(1); // Reset to first page when changing category
    } catch (error) {
      console.error('Error in handleCategorySelect:', error);
    }
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const handleCreateCollection = () => {
    console.log('Opening collection dialog');
    setCreateDialogOpen(true);
  };

  const handleCloseCollectionDialog = () => {
    console.log('Closing collection dialog');
    setCreateDialogOpen(false);
  };

  const handleCreateCategory = () => {
    // Reset form fields
    setCategoryName('');
    setCategoryDescription('');
    setCreateCategoryDialogOpen(true);
  };

  const handleCreateCategorySubmit = async () => {
    if (!categoryName.trim()) {
      toast({
        title: "Validation Error",
        description: "Category name is required",
        variant: "destructive"
      });
      return;
    }

    try {
      // First check database connection
      const isConnected = await checkDatabaseConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      console.log('Creating category with name:', categoryName.trim());

      // Create the category data
      const categoryData = {
        id: null, // Let the backend assign the ID
        categoryName: categoryName.trim()
      };

      console.log('Creating category with data:', categoryData);

      // Use the API service instead of direct fetch
      try {
        const response = await categoriesApi.create(categoryData);
        console.log('Category created successfully:', response.data);
      } catch (apiError: any) {
        console.error('API error:', apiError);
        throw new Error(`Failed to create category: ${apiError.message}`);
      }

      toast({
        title: "Category created",
        description: `Category "${categoryName}" has been created successfully`,
      });

      // Refresh categories list
      try {
        const categoriesResponse = await categoriesApi.getAll();
        setCategories(categoriesResponse.data || []);
      } catch (refreshError) {
        console.error('Error refreshing categories:', refreshError);
      }

      // Close the dialog and reset form
      setCreateCategoryDialogOpen(false);
      setCategoryName('');
      setCategoryDescription('');
    } catch (error: any) {
      console.error('Error creating category:', error);

      // Extract more specific error message if available
      let errorMessage = 'Failed to create category: ' + error.message;

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleSaveCollection = async (data: any) => {
    try {
      console.log('Handling save collection with data:', data);

      // First check database connection
      const isConnected = await checkDatabaseConnection();
      if (!isConnected) {
        throw new Error('Database connection failed');
      }

      // Get all collections to determine the next ID
      const collectionsResponse = await collectionsApi.getAll();
      const existingCollections = collectionsResponse.data || [];

      // Find the highest ID and add 1, or start with 1 if no collections exist
      const nextId = existingCollections.length > 0
        ? Math.max(...existingCollections.map((c: any) => parseInt(c.id))) + 1
        : 1;

      const collectionData = {
        id: nextId,
        collectionName: data.name,
        collectionDesc: `${data.name} collection`,
        collectionApiId: data.apiIdSingular,
        // Store additional properties in the description field as JSON
        additionalInformation: JSON.stringify({
          apiIdPlural: data.apiIdPlural,
          draftAndPublish: data.draftAndPublish,
          isInternationally: data.isInternationally
        }),
        fields: [],
      };

      // Add category ID if selected
      if (data.categoryId && data.categoryId !== '') {
        console.log(`Adding category ID ${data.categoryId} to collection`);
        collectionData.category = { id: parseInt(data.categoryId) };
      } else {
        console.log('No category selected for this collection');
      }

      console.log('Creating collection with data:', collectionData);
      try {
        const response = await collectionsApi.create(collectionData);
        console.log('Collection created successfully:', response.data);

        // Format the collection data to match the expected structure in the store
        const formattedCollection = {
          id: response.data.id.toString(),
          name: response.data.collectionName,
          apiId: response.data.collectionApiId,
          apiIdPlural: data.apiIdPlural,
          draftAndPublish: data.draftAndPublish,
          isInternationally: data.isInternationally,
          fields: [],
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          category: response.data.category || (data.categoryId ? { id: parseInt(data.categoryId) } : null),
          categoryName: response.data.categoryName || ''
        };

        // Log category information for debugging
        if (data.categoryId) {
          console.log(`Collection created with category ID: ${data.categoryId}`);
        } else {
          console.log('Collection created without a category');
        }

        console.log('Adding formatted collection to store:', formattedCollection);
        addCollection(formattedCollection);

        toast({
          title: 'Content type created',
          description: 'Your content type has been created successfully',
        });

        console.log('Collection created successfully, closing dialog');
        setCreateDialogOpen(false);

        // Navigate to the edit page with a slight delay to ensure state is updated
        setTimeout(() => {
          console.log('Navigating to edit page');
          console.log('Navigating to edit page for collection ID:', response.data.id);
          navigate(`/content-types/edit/${response.data.id}`);
        }, 500);
      } catch (apiError: any) {
        console.error('API error creating collection:', apiError);

        // Extract more specific error message if available
        let errorMessage = 'Failed to create collection';

        if (apiError.response) {
          console.error('Error response:', apiError.response.data);

          if (apiError.response.status === 409) {
            errorMessage = 'A collection with this name already exists';
          } else if (apiError.response.data && apiError.response.data.message) {
            errorMessage = apiError.response.data.message;
          }
        } else if (apiError.request) {
          errorMessage = 'No response from server. Please check your connection';
        }

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });

        // Don't close the dialog on error so the user can try again
        console.log('Error occurred, keeping dialog open');
      }

      // The collection creation and navigation is now handled in the try-catch block above
    } catch (error: any) {
      console.error('Error creating content type:', error);

      // Extract more specific error message if available
      let errorMessage = 'Failed to create collection';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response:', error.response.data);

        if (error.response.status === 409) {
          errorMessage = 'A collection with this name already exists';
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'No response from server. Please check your connection';
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleEditCollection = (id: string) => {
    navigate(`/content-types/edit/${id}`);
  };

  const confirmDeleteCollection = (id: string) => {
    setCollectionToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteCollection = async () => {
    if (!collectionToDelete) return;

    try {
      await collectionsApi.delete(collectionToDelete);
      removeCollection(collectionToDelete);
      toast({
        title: 'Success',
        description: 'Content type deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting collection:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete content type',
        variant: 'destructive',
      });
    } finally {
      setDeleteConfirmOpen(false);
      setCollectionToDelete(null);
    }
  };

  // Handle rename category
  const handleRenameCategory = (category: any) => {
    setSelectedCategory(category);
    setRenameCategoryDialogOpen(true);
  };

  // Handle save renamed category
  const handleSaveRenamedCategory = async (data: { categoryName: string }) => {
    if (!selectedCategory) return;

    try {
      // Create the category data for update
      const categoryData = {
        categoryName: data.categoryName,
        description: selectedCategory.description || ''
      };

      console.log('Updating category with data:', categoryData);

      // Call the API to update the category
      const response = await categoriesApi.update(selectedCategory.id.toString(), categoryData);
      console.log('Category updated successfully:', response.data);

      // Update the category in the local state
      const updatedCategories = categories.map(cat =>
        cat.id === selectedCategory.id ? { ...cat, categoryName: data.categoryName } : cat
      );
      setCategories(updatedCategories);

      toast({
        title: 'Success',
        description: 'Category renamed successfully',
      });

      setRenameCategoryDialogOpen(false);
    } catch (error) {
      console.error('Error renaming category:', error);
      toast({
        title: 'Error',
        description: 'Failed to rename category',
        variant: 'destructive',
      });
    }
  };

  // Handle delete category
  const handleDeleteCategory = (category: any) => {
    setSelectedCategory(category);
    setDeleteCategoryDialogOpen(true);
  };

  // Handle confirm delete category
  const handleConfirmDeleteCategory = async () => {
    if (!selectedCategory) return;

    try {
      // Call the API to delete the category
      await categoriesApi.delete(selectedCategory.id.toString());
      console.log('Category deleted successfully');

      // Remove the category from the local state
      const updatedCategories = categories.filter(cat => cat.id !== selectedCategory.id);
      setCategories(updatedCategories);

      toast({
        title: 'Success',
        description: 'Category deleted successfully',
      });

      setDeleteCategoryDialogOpen(false);
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete category',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold">Categories</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleCreateCategory} variant="outline">
            <Folder className="mr-2 h-4 w-4" />
            Create Category
          </Button>
        </div>
      </div>

      {loading ? (
        // Loading state
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="animate-pulse p-4 border rounded-md">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-200 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="h-5 w-1/4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 w-1/3 bg-gray-200 rounded"></div>
                </div>
                <div className="h-8 w-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : collections.length === 0 ? (
        // Empty state
        <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-lg">
          <Database className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No content types found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Create your first content type to start building your API
          </p>
          <div className="flex gap-2">
            <Button onClick={handleCreateCategory} variant="outline">
              <Folder className="mr-2 h-4 w-4" />
              Create Category
            </Button>
            <Button onClick={handleCreateCollection}>
              <Plus className="mr-2 h-4 w-4" />
              Create new collection
            </Button>
          </div>
        </div>
      ) : (
        // List of collections
        <>
          {selectedCategoryId ? (
            // Simple display of collections for selected category
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Collections in {categories.find(c => c.id.toString() === selectedCategoryId)?.categoryName}</h3>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-yellow-100"
                    onClick={() => {
                      // Debug function
                      console.log('DEBUG - Selected category ID:', selectedCategoryId);
                      console.log('DEBUG - All collections:', collections);

                      // Try to find collections with this category
                      const categoryIdNum = parseInt(selectedCategoryId);
                      const matchingCollections = collections.filter(c => {
                        if (!c || !c.category) return false;

                        let categoryId = null;
                        if (typeof c.category === 'object') categoryId = c.category.id;
                        else if (typeof c.category === 'number') categoryId = c.category;
                        else if (typeof c.category === 'string') categoryId = parseInt(c.category);

                        return categoryId === categoryIdNum;
                      });

                      console.log(`DEBUG - Found ${matchingCollections.length} collections in category ${selectedCategoryId}:`, matchingCollections);

                      // Force a re-render
                      setCollections([...collections]);
                    }}
                  >
                    Debug
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedCategoryId(null)}
                  >
                    View All Collections
                  </Button>
                </div>
              </div>

              {/* Super simple list of collections */}
              <div className="border rounded-md overflow-hidden">
                {/* Hardcoded example collection for testing */}
                <div className="p-4 border-b hover:bg-muted/50 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 border border-border">
                        P
                      </div>
                      <div>
                        <h3 className="font-medium">Product Page</h3>
                        <p className="text-xs text-muted-foreground">API ID: product_page</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        2 fields
                      </Badge>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Actual filtered collections */}
                {collections
                  .filter(collection => {
                    // Simple filtering logic
                    try {
                      if (!collection || !collection.category) return false;

                      const categoryIdNum = parseInt(selectedCategoryId);
                      let collectionCategoryId = null;

                      if (typeof collection.category === 'object' && collection.category !== null) {
                        collectionCategoryId = collection.category.id;
                      } else if (typeof collection.category === 'number') {
                        collectionCategoryId = collection.category;
                      } else if (typeof collection.category === 'string') {
                        collectionCategoryId = parseInt(collection.category);
                      }

                      return collectionCategoryId === categoryIdNum;
                    } catch (e) {
                      console.error('Error filtering collection:', e);
                      return false;
                    }
                  })
                  .map((collection, index, filteredArray) => (
                    <div
                      key={collection.id}
                      className="p-4 border-b hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleEditCollection(collection.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 border border-border">
                            {(collection.name || 'U')[0].toUpperCase()}
                          </div>
                          <div>
                            <h3 className="font-medium">{collection.name || 'Unnamed Collection'}</h3>
                            <p className="text-xs text-muted-foreground">API ID: {collection.apiId}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {collection.fields?.length || 0} fields
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditCollection(collection.id);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                            onClick={(e) => {
                              e.stopPropagation();
                              confirmDeleteCollection(collection.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                }

                {/* Message when no collections found */}
                {collections.filter(c => {
                  if (!c || !c.category) return false;
                  const categoryIdNum = parseInt(selectedCategoryId);
                  let categoryId = null;
                  if (typeof c.category === 'object') categoryId = c.category.id;
                  else if (typeof c.category === 'number') categoryId = c.category;
                  else if (typeof c.category === 'string') categoryId = parseInt(c.category);
                  return categoryId === categoryIdNum;
                }).length === 0 && (
                  <div className="p-4 text-center text-muted-foreground">
                    No collections found in this category
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex justify-between items-center mb-4">
              <div className="text-sm text-muted-foreground">
                {searchQuery.trim() && (
                  <span className="font-medium mr-2">Search results for "{searchQuery}"</span>
                )}
                <span className="pagination-info">Showing items</span>
              </div>
              <div className="items-per-page-selector">
                <span className="text-sm">Items per page:</span>
                <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="5" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="15">15</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )
          }
          {getFilteredCollections().length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 bg-gray-50 border border-dashed rounded-lg">
              <Database className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium">No collections found</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {searchQuery && selectedCategoryId ? (
                  <>No collections match your search query "{searchQuery}" in the selected category</>
                ) : searchQuery ? (
                  <>No collections match your search query "{searchQuery}"</>
                ) : selectedCategoryId ? (
                  <>No collections found in the selected category "{categories.find(c => c.id.toString() === selectedCategoryId)?.categoryName}"</>
                ) : (
                  <>No collections have been created yet</>
                )}
              </p>
              <div className="flex gap-2">
                {searchQuery && (
                  <Button variant="outline" onClick={() => setSearchQuery('')}>
                    Clear search
                  </Button>
                )}
                {selectedCategoryId && (
                  <Button variant="outline" onClick={() => setSelectedCategoryId(null)}>
                    Clear category filter
                  </Button>
                )}
                {!searchQuery && !selectedCategoryId && (
                  <Button onClick={handleCreateCollection}>
                    Create collection
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="border rounded-md overflow-hidden">
              {getPaginatedCollections().map((collection, index) => (
              <div
                key={collection.id}
                className={`flex items-center justify-between p-4 hover:bg-muted/50 ${index !== getPaginatedCollections().length - 1 ? 'border-b' : ''} cursor-pointer transition-colors duration-200`}
                onClick={() => handleEditCollection(collection.id)}
              >
                <div className="flex items-center flex-1">
                  <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mr-3 flex-shrink-0 border border-border">
                    {(collection.name || 'U')[0].toUpperCase()}
                  </div>
                  <div>
                    <h3 className="font-medium">{collection.name || 'Unnamed Collection'}</h3>
                    <p className="text-sm text-muted-foreground">API ID: {collection.apiId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {collection.category && (
                    <Badge variant="outline" className="text-xs">
                      {categories.find(c => c.id === (typeof collection.category === 'object' ? collection.category.id : collection.category))?.categoryName || 'Category'}
                    </Badge>
                  )}
                  <div className="bg-primary/10 text-primary text-xs font-medium px-2 py-1 rounded-full">
                    {collection.fields?.length || 0} fields
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditCollection(collection.id);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    onClick={(e) => {
                      e.stopPropagation();
                      confirmDeleteCollection(collection.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            </div>
          )}

          {/* Pagination controls - always show */}
          <div className="mt-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      isActive={page === currentPage}
                      onClick={() => handlePageChange(page)}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </>
      )}

      {/* Delete confirmation dialog */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete content type</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this content type? This action cannot be undone and all related content will be permanently removed.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteCollection}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create collection type dialog */}
      <BasicCollectionDialog
        isOpen={createDialogOpen}
        onClose={handleCloseCollectionDialog}
        onSave={handleSaveCollection}
      />

      {/* Create category dialog - will be implemented */}
      <Dialog open={createCategoryDialogOpen} onOpenChange={setCreateCategoryDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                C
              </div>
              <DialogTitle>Create a category</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Category</h3>
              <p className="text-sm text-muted-foreground">Create a category to organize your collections</p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="categoryName" className="text-sm font-medium">Category Name</label>
                  <Input
                    id="categoryName"
                    placeholder="Enter category name"
                    value={categoryName}
                    onChange={(e) => setCategoryName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="categoryDescription" className="text-sm font-medium">Description (optional)</label>
                  <Input
                    id="categoryDescription"
                    placeholder="Enter category description"
                    value={categoryDescription}
                    onChange={(e) => setCategoryDescription(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateCategoryDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateCategorySubmit} disabled={!categoryName.trim()}>Create Category</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Categories Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Categories</h2>
          <Button onClick={handleCreateCategory} variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        </div>

        {loadingCategories ? (
          <div className="flex justify-center p-4">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : categories.length === 0 ? (
          <div className="bg-muted/50 rounded-md p-6 text-center">
            <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No categories found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Create categories to organize your collections
            </p>
            <Button onClick={handleCreateCategory} variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Create Category
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {categories.map((category) => (
              <div
                key={category.id}
                className={`bg-card border rounded-md shadow-sm hover:shadow-md transition-shadow cursor-pointer ${selectedCategoryId === category.id.toString() ? 'ring-2 ring-primary' : ''}`}
                onClick={() => navigate(`/content-types/category/${category.id}`)}
              >
                <div className="p-4 border-b">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full ${selectedCategoryId === category.id.toString() ? 'bg-primary text-white' : 'bg-primary/10 text-primary'} flex items-center justify-center mr-3`}>
                      <Folder className="h-4 w-4" />
                    </div>
                    <h3 className="font-medium">{category.categoryName}</h3>
                  </div>
                </div>
                <div className="p-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    {(() => {
                      try {
                        // Safely count collections in this category
                        return collections.filter(c => {
                          if (!c) return false;
                          const categoryId = getCategoryId(c);
                          return categoryId === category.id;
                        }).length;
                      } catch (error) {
                        console.error('Error counting collections:', error);
                        return 0;
                      }
                    })()} collections
                  </div>
                  <div className="flex items-center gap-2">
                    <div onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleRenameCategory(category)}>
                            <Pencil className="h-4 w-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteCategory(category)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary hover:bg-primary/90 transition-all"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}

      {/* Rename category dialog */}
      {selectedCategory && (
        <RenameCategoryDialog
          isOpen={renameCategoryDialogOpen}
          onClose={() => setRenameCategoryDialogOpen(false)}
          onSave={handleSaveRenamedCategory}
          initialName={selectedCategory.categoryName}
        />
      )}

      {/* Delete category dialog */}
      {selectedCategory && (
        <DeleteCategoryDialog
          isOpen={deleteCategoryDialogOpen}
          onClose={() => setDeleteCategoryDialogOpen(false)}
          onConfirm={handleConfirmDeleteCategory}
          categoryName={selectedCategory.categoryName}
          collectionCount={collections.filter(c => {
            if (!c) return false;
            const categoryId = getCategoryId(c);
            return categoryId === selectedCategory.id;
          }).length}
        />
      )}
    </div>
  );
}
