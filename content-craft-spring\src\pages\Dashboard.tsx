
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Database, FileText, Layers, Users } from 'lucide-react';
import { useCollectionStore, useContentEntryStore } from '@/lib/store';
import { collectionsApi, contentEntriesApi } from '@/lib/api';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function Dashboard() {
  const navigate = useNavigate();
  const { collections, setCollections, setLoading: setCollectionsLoading } = useCollectionStore();
  const { contentEntries, setContentEntries, setLoading: setEntriesLoading } = useContentEntryStore();
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchDashboardData = async () => {
      // Check if we already have collections and content entries in the store
      if (collections.length > 0 && contentEntries.length > 0) {
        console.log('Using cached dashboard data');
        return;
      }

      setCollectionsLoading(true);
      setEntriesLoading(true);

      try {
        // Fetch collections only if we don't have them
        if (collections.length === 0) {
          console.log('Fetching collections from API');
          const collectionsResponse = await collectionsApi.getAll();
          setCollections(collectionsResponse.data);
        }

        // Fetch content entries only if we don't have them
        if (contentEntries.length === 0) {
          console.log('Fetching content entries from API');
          const entriesResponse = await contentEntriesApi.getAll();
          setContentEntries(entriesResponse.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setCollectionsLoading(false);
        setEntriesLoading(false);
      }
    };

    fetchDashboardData();
  }, [collections.length, contentEntries.length, setCollections, setContentEntries, setCollectionsLoading, setEntriesLoading]);

  // Dashboard stats
  const stats = [
    {
      title: 'Collections',
      value: collections.length,
      icon: Database,
      color: 'bg-blue-500',
      path: '/content-types',
    },
    {
      title: 'Content Entries',
      value: contentEntries.length,
      icon: FileText,
      color: 'bg-green-500',
      path: '/content-manager',
    },
    {
      title: 'Media Items',
      value: 0, // Placeholder
      icon: Layers,
      color: 'bg-purple-500',
      path: '/media-library',
    },
    {
      title: 'Users',
      value: 1, // Placeholder
      icon: Users,
      color: 'bg-orange-500',
      path: '/settings/users',
    },
  ];

  // Function to manually refresh dashboard data
  const refreshDashboardData = async () => {
    setIsLoading(true);
    setCollectionsLoading(true);
    setEntriesLoading(true);

    try {
      console.log('Manually refreshing dashboard data');
      const collectionsResponse = await collectionsApi.getAll();
      setCollections(collectionsResponse.data);

      const entriesResponse = await contentEntriesApi.getAll();
      setContentEntries(entriesResponse.data);
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    } finally {
      setIsLoading(false);
      setCollectionsLoading(false);
      setEntriesLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button variant="outline" size="sm" onClick={refreshDashboardData} disabled={isLoading}>
            {isLoading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
        <Button onClick={() => navigate('/content-types/create')}>Create Content Type</Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-10 w-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                  <div className={`p-2 rounded-full ${stat.color}`}>
                    <stat.icon className="h-4 w-4 text-white" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{stat.value}</p>
              </CardContent>
              <CardFooter>
                <Button variant="ghost" className="px-0 hover:bg-transparent" onClick={() => navigate(stat.path)}>
                  View all
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Recent Activity and Content Distribution sections have been removed */}
    </div>
  );
}
