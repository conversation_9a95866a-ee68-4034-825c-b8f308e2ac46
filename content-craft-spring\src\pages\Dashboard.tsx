
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Database, FileText, Layers, Users, Plus, RefreshCw } from 'lucide-react';
import { useCollectionStore, useContentEntryStore, useAuthStore } from '@/lib/store';
import { collectionsApi, contentEntriesApi } from '@/lib/api';

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, token } = useAuthStore();
  const { collections, setCollections, setLoading: setCollectionsLoading } = useCollectionStore();
  const { contentEntries, setContentEntries, setLoading: setEntriesLoading } = useContentEntryStore();
  const [isLoading, setIsLoading] = React.useState(true);
  const [debugInfo, setDebugInfo] = React.useState<any>(null);

  React.useEffect(() => {
    const fetchDashboardData = async () => {
      // Check if we already have collections and content entries in the store
      if (collections.length > 0 && contentEntries.length > 0) {
        console.log('Using cached dashboard data');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setCollectionsLoading(true);
      setEntriesLoading(true);

      try {
        // Set debug info
        setDebugInfo({
          user: user?.username || 'Unknown',
          tenant: user?.tenant || 'Unknown',
          hasToken: !!token,
          tokenPreview: token ? `${token.substring(0, 20)}...` : 'No token'
        });

        // Fetch collections only if we don't have them
        if (collections.length === 0) {
          console.log('Fetching collections from API');
          console.log('Current user:', user);
          console.log('Current token:', token ? 'Present' : 'Missing');
          try {
            const collectionsResponse = await collectionsApi.getAll();
            console.log('Collections API response:', collectionsResponse);

            // Handle both array and non-array responses
            const collectionsData = Array.isArray(collectionsResponse.data)
              ? collectionsResponse.data
              : [];

            setCollections(collectionsData);
            console.log('Collections set:', collectionsData.length);
          } catch (collectionsError) {
            console.error('Error fetching collections:', collectionsError);
            // Set empty array if there's an error
            setCollections([]);
          }
        }

        // Fetch content entries only if we don't have them
        if (contentEntries.length === 0) {
          console.log('Fetching content entries from API');
          try {
            const entriesResponse = await contentEntriesApi.getAll();
            console.log('Content entries API response:', entriesResponse);

            // Handle both array and non-array responses
            const entriesData = Array.isArray(entriesResponse.data)
              ? entriesResponse.data
              : [];

            setContentEntries(entriesData);
            console.log('Content entries set:', entriesData.length);
          } catch (entriesError) {
            console.error('Error fetching content entries:', entriesError);
            // Set empty array if there's an error
            setContentEntries([]);
          }
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Ensure we have empty arrays even if there's an error
        setCollections([]);
        setContentEntries([]);
      } finally {
        setIsLoading(false);
        setCollectionsLoading(false);
        setEntriesLoading(false);
      }
    };

    fetchDashboardData();
  }, [collections.length, contentEntries.length, setCollections, setContentEntries, setCollectionsLoading, setEntriesLoading]);

  // Dashboard stats
  const stats = [
    {
      title: 'Collections',
      value: collections.length,
      icon: Database,
      color: 'bg-blue-500',
      path: '/content-types',
    },
    {
      title: 'Content Entries',
      value: contentEntries.length,
      icon: FileText,
      color: 'bg-green-500',
      path: '/content-manager',
    },
    {
      title: 'Media Items',
      value: 0, // Placeholder
      icon: Layers,
      color: 'bg-purple-500',
      path: '/media-library',
    },
    {
      title: 'Users',
      value: 1, // Placeholder
      icon: Users,
      color: 'bg-orange-500',
      path: '/settings/users',
    },
  ];

  // Function to manually refresh dashboard data
  const refreshDashboardData = async () => {
    setIsLoading(true);
    setCollectionsLoading(true);
    setEntriesLoading(true);

    try {
      console.log('Manually refreshing dashboard data');

      // Fetch collections with error handling
      try {
        const collectionsResponse = await collectionsApi.getAll();
        console.log('Refresh - Collections API response:', collectionsResponse);

        const collectionsData = Array.isArray(collectionsResponse.data)
          ? collectionsResponse.data
          : [];

        setCollections(collectionsData);
        console.log('Refresh - Collections set:', collectionsData.length);
      } catch (collectionsError) {
        console.error('Error refreshing collections:', collectionsError);
        setCollections([]);
      }

      // Fetch content entries with error handling
      try {
        const entriesResponse = await contentEntriesApi.getAll();
        console.log('Refresh - Content entries API response:', entriesResponse);

        const entriesData = Array.isArray(entriesResponse.data)
          ? entriesResponse.data
          : [];

        setContentEntries(entriesData);
        console.log('Refresh - Content entries set:', entriesData.length);
      } catch (entriesError) {
        console.error('Error refreshing content entries:', entriesError);
        setContentEntries([]);
      }

    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
      // Ensure we have empty arrays even if there's an error
      setCollections([]);
      setContentEntries([]);
    } finally {
      setIsLoading(false);
      setCollectionsLoading(false);
      setEntriesLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button variant="outline" size="sm" onClick={refreshDashboardData} disabled={isLoading}>
            {isLoading ? 'Refreshing...' : 'Refresh Data'}
          </Button>
        </div>
        <Button onClick={() => navigate('/content-types/create')}>Create Content Type</Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-10 w-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                    <div className={`p-2 rounded-full ${stat.color}`}>
                      <stat.icon className="h-4 w-4 text-white" />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{stat.value}</p>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="px-0 hover:bg-transparent" onClick={() => navigate(stat.path)}>
                    View all
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* Empty State for New Tenants */}
          {collections.length === 0 && contentEntries.length === 0 && !isLoading && (
            <Card className="mt-6">
              <CardHeader className="text-center">
                <CardTitle className="flex items-center justify-center space-x-2">
                  <Database className="h-6 w-6" />
                  <span>Welcome to Your CMS Dashboard!</span>
                </CardTitle>
                <CardDescription>
                  It looks like you're just getting started. Create your first content type to begin building your content structure.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="p-4 border rounded-lg">
                    <Database className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                    <h4 className="font-semibold">1. Create Content Types</h4>
                    <p className="text-muted-foreground">Define the structure of your content</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <FileText className="h-8 w-8 mx-auto mb-2 text-green-500" />
                    <h4 className="font-semibold">2. Add Content</h4>
                    <p className="text-muted-foreground">Create and manage your content entries</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <Layers className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                    <h4 className="font-semibold">3. Organize Media</h4>
                    <p className="text-muted-foreground">Upload and manage your media files</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center space-x-2">
                <Button onClick={() => navigate('/content-types/create')} className="flex items-center space-x-2">
                  <Plus className="h-4 w-4" />
                  <span>Create Your First Content Type</span>
                </Button>
                <Button variant="outline" onClick={refreshDashboardData} className="flex items-center space-x-2">
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh Data</span>
                </Button>
              </CardFooter>
            </Card>
          )}

          {/* Debug Information Panel */}
          {debugInfo && (
            <Card className="mt-6 border-dashed">
              <CardHeader>
                <CardTitle className="text-sm text-muted-foreground">Debug Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                  <div>
                    <strong>User:</strong> {debugInfo.user}
                  </div>
                  <div>
                    <strong>Tenant:</strong> {debugInfo.tenant}
                  </div>
                  <div>
                    <strong>Token:</strong> {debugInfo.hasToken ? '✅ Present' : '❌ Missing'}
                  </div>
                  <div>
                    <strong>Collections:</strong> {collections.length}
                  </div>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  <strong>Token Preview:</strong> {debugInfo.tokenPreview}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Recent Activity and Content Distribution sections have been removed */}
    </div>
  );
}
