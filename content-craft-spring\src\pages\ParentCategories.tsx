import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Plus, ArrowUp, Search, Building, Edit, MoreVertical, Trash2, Pencil, ArrowLeft } from "lucide-react";
import { clientsApi, parentCategoriesApi, categoriesApi, collectionsApi } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { DeleteCategoryDialog } from '@/components/dialogs/DeleteCategoryDialog';
import { useClickedClient, useClickedParentCategory } from "@/lib/store";

interface ParentCategories {
  id: number;
  name: string;
  categoryName: string;
}

export default function ParentCategories() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [clients, setClients] = useState<ParentCategories[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [createClientDialogOpen, setCreateClientDialogOpen] = useState(false);
  const [editClientDialogOpen, setEditClientDialogOpen] = useState(false);
  const [deleteClientDialogOpen, setDeleteClientDialogOpen] = useState(false);
  const [clientName, setClientName] = useState("");
  const [editingClient, setEditingClient] = useState<ParentCategories | null>(null);
  const [editClientName, setEditClientName] = useState("");
  const [selectedClient, setSelectedClient] = useState<ParentCategories | null>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [parentCategoryCounts, setParentCategoryCounts] = useState<Record<number, { categories: number; collections: number }>>({});

  const { clientId, setClientId } = useClickedClient();

  //   alert(clientId)

  // Fetch clients on component mount
  useEffect(() => {
    fetchClients();

    // Add scroll listener
    const handleScroll = () => {
      setShowScrollButton(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const response = await parentCategoriesApi.getByClientId(clientId);
      console.log(response, "responseresponseresponse");
      const parentCategories = response.data || [];
      setClients(parentCategories);

      // Fetch counts for each parent category
      const counts: Record<number, { categories: number; collections: number }> = {};

      for (const parentCategory of parentCategories) {
        try {
          // Get categories under this parent category
          const categoriesResponse = await categoriesApi.getByParentCategoryId(parentCategory.id.toString());
          const categories = categoriesResponse.data || [];
          const categoryCount = categories.length;

          // Get total collections across all categories in this parent category
          let totalCollections = 0;
          for (const category of categories) {
            try {
              const collectionsResponse = await collectionsApi.getByCategoryId(category.id.toString());
              const collections = collectionsResponse.data || [];
              totalCollections += collections.length;
            } catch (error) {
              console.error(`Error fetching collections for category ${category.id}:`, error);
            }
          }

          counts[parentCategory.id] = {
            categories: categoryCount,
            collections: totalCollections
          };

          console.log(`Parent Category ${parentCategory.categoryName}: ${categoryCount} categories, ${totalCollections} collections`);
        } catch (error) {
          console.error(`Error fetching data for parent category ${parentCategory.id}:`, error);
          counts[parentCategory.id] = { categories: 0, collections: 0 };
        }
      }

      setParentCategoryCounts(counts);
      console.log('Parent category counts:', counts);
    } catch (error) {
      console.error("Error fetching clients:", error);
      toast({
        title: "Error",
        description: "Failed to load clients",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateClient = async () => {
    if (!clientName.trim()) {
      toast({
        title: "Validation Error",
        description: "Client name is required",
        variant: "destructive",
      });
      return;
    }

    try {
      //      {
      // "categoryName": "Saurabh",
      // "description": "All smartphone models",
      // "parentCategoryId": 101,
      // "client": {
      //     "id": 101,
      //     "name": "Saurabh"
      // }
      // }
      const parentCatData = {
        categoryName: clientName.trim(),
        client: {
          id: clientId,
        },
      };

      const response = await parentCategoriesApi.create(parentCatData);
      console.log("Client created successfully:", response.data);

      toast({
        title: "Client created",
        description: `Client "${clientName}" has been created successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setCreateClientDialogOpen(false);
      setClientName("");
    } catch (error: any) {
      console.error("Error creating client:", error);
      toast({
        title: "Error",
        description: "Failed to create client: " + error.message,
        variant: "destructive",
      });
    }
  };

  // Handle edit client
  const handleEditClient = (client: ParentCategories) => {
    setEditingClient(client);
    setEditClientName(client.categoryName);
    setEditClientDialogOpen(true);
  };

  // Handle update client
  const handleUpdateClient = async () => {
    if (!editClientName.trim() || !editingClient) {
      toast({
        title: "Error",
        description: "Parent Category name cannot be empty",
        variant: "destructive",
      });
      return;
    }

    try {
      const parentCatData = {
        categoryName: editClientName.trim(),
        client: {
          id: clientId,
        },
      };

      await parentCategoriesApi.update(editingClient.id.toString(), parentCatData);

      toast({
        title: "Parent Category updated",
        description: `Parent Category "${editClientName}" has been updated successfully`,
      });

      // Refresh clients list
      fetchClients();

      // Close the dialog and reset form
      setEditClientDialogOpen(false);
      setEditingClient(null);
      setEditClientName("");
    } catch (error: any) {
      console.error("Error updating parent category:", error);
      toast({
        title: "Error",
        description: "Failed to update parent category: " + (error.response?.data?.message || error.message),
        variant: "destructive",
      });
    }
  };

  // Handle delete parent category
  const handleDeleteClient = (client: ParentCategories) => {
    setSelectedClient(client);
    setDeleteClientDialogOpen(true);
  };

  // Handle confirm delete parent category
  const handleConfirmDeleteClient = async () => {
    if (!selectedClient) return;

    try {
      // Call the API to delete the parent category
      await parentCategoriesApi.delete(selectedClient.id.toString());
      console.log('Parent category deleted successfully');

      // Remove the parent category from the local state
      const updatedClients = clients.filter(client => client.id !== selectedClient.id);
      setClients(updatedClients);

      // Update counts by removing the deleted parent category
      const updatedCounts = { ...parentCategoryCounts };
      delete updatedCounts[selectedClient.id];
      setParentCategoryCounts(updatedCounts);

      toast({
        title: 'Success',
        description: 'Parent category deleted successfully',
      });

      setDeleteClientDialogOpen(false);
      setSelectedClient(null);
    } catch (error) {
      console.error('Error deleting parent category:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete parent category',
        variant: 'destructive',
      });
    }
  };

  // Filter clients based on search query
  const getFilteredClients = () => {
    if (!searchQuery.trim()) return clients;

    return clients.filter((client) =>
      client?.categoryName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/clients')}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Parent Categories</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setCreateClientDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Parent Category
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search clients..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : getFilteredClients().length === 0 ? (
          <div className="bg-muted/50 rounded-md p-6 text-center">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            {/* <h3 className="text-lg font-medium mb-2">No clients found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              {searchQuery ? 'No clients match your search query' : 'Create your first client to get started'}
            </p> */}
            <Button onClick={() => setCreateClientDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Parent Category
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {getFilteredClients().map((client) => (
              <div
                key={client.id}
                className="bg-card border rounded-md shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => {
                  navigate(`/content-types/`);
                  useClickedParentCategory
                    .getState()
                    .setParentCategoryId(client.id);
                }}
              >
                <div className="p-4 border-b">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center mr-3">
                      <Building className="h-4 w-4" />
                    </div>
                    <h3 className="font-medium">{client.categoryName}</h3>
                  </div>
                </div>
                <div className="p-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    {loading ? (
                      <span className="inline-flex items-center">
                        <span className="w-3 h-3 mr-1 rounded-full animate-pulse bg-primary/50"></span>
                        Loading...
                      </span>
                    ) : (
                      <div className="flex flex-col gap-1">
                        <span>
                          {parentCategoryCounts[client.id]?.categories || 0} categories
                        </span>
                        <span>
                          {parentCategoryCounts[client.id]?.collections || 0} collections
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <div onClick={(e) => e.stopPropagation()}>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-muted">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditClient(client)}>
                            <Pencil className="h-4 w-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteClient(client)}>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>

              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create client dialog */}
      <Dialog
        open={createClientDialogOpen}
        onOpenChange={setCreateClientDialogOpen}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                C
              </div>
              <DialogTitle>Create a Parent Category</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Parent Category</h3>
              <p className="text-sm text-muted-foreground">
                Create a Parent Category to organize your Categories
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="clientName" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="clientName"
                    placeholder="Enter Parent Category name"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setCreateClientDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateClient}>Create Parent Category</Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit parent category dialog */}
      <Dialog open={editClientDialogOpen} onOpenChange={setEditClientDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader className="flex flex-row items-center">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 mr-2 bg-primary text-primary-foreground rounded">
                <Edit className="h-4 w-4" />
              </div>
              <DialogTitle>Edit Parent Category</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <h3 className="text-lg font-medium">Rename Parent Category</h3>
              <p className="text-sm text-muted-foreground">
                Update the parent category name
              </p>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label htmlFor="editClientName" className="text-sm font-medium">
                    Name
                  </label>
                  <Input
                    id="editClientName"
                    placeholder="Enter parent category name"
                    value={editClientName}
                    onChange={(e) => setEditClientName(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleUpdateClient();
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setEditClientDialogOpen(false);
                setEditingClient(null);
                setEditClientName('');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateClient}>
              Update Parent Category
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete parent category dialog */}
      {selectedClient && (
        <DeleteCategoryDialog
          isOpen={deleteClientDialogOpen}
          onClose={() => {
            setDeleteClientDialogOpen(false);
            setSelectedClient(null);
          }}
          onConfirm={handleConfirmDeleteClient}
          categoryName={selectedClient.categoryName}
          collectionCount={(parentCategoryCounts[selectedClient.id]?.categories || 0) + (parentCategoryCounts[selectedClient.id]?.collections || 0)}
        />
      )}

      {/* Scroll to top button */}
      {showScrollButton && (
        <Button
          className="fixed bottom-6 right-6 rounded-full w-12 h-12 shadow-lg flex items-center justify-center bg-primary hover:bg-primary/90 transition-all"
          onClick={scrollToTop}
          aria-label="Scroll to top"
        >
          <ArrowUp className="h-5 w-5 text-white" />
        </Button>
      )}
    </div>
  );
}
