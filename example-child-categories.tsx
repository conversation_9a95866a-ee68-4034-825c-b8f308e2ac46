import React, { useState, useEffect } from 'react';
import { categoriesApi } from '@/lib/api';

interface Category {
  id: number;
  categoryName: string;
  parentCategory?: {
    id: number;
    categoryName: string;
  };
  client: {
    id: number;
    name: string;
  };
}

interface ChildCategoriesProps {
  parentCategoryId: number;
}

const ChildCategories: React.FC<ChildCategoriesProps> = ({ parentCategoryId }) => {
  const [childCategories, setChildCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChildCategories = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await categoriesApi.getByParentCategoryId(parentCategoryId.toString());
        setChildCategories(response.data);
      } catch (err: any) {
        setError('Failed to load child categories');
        console.error('Error fetching child categories:', err);
      } finally {
        setLoading(false);
      }
    };

    if (parentCategoryId) {
      fetchChildCategories();
    }
  }, [parentCategoryId]);

  if (loading) {
    return <div>Loading child categories...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  if (childCategories.length === 0) {
    return <div>No child categories found.</div>;
  }

  return (
    <div>
      <h3>Child Categories</h3>
      <ul>
        {childCategories.map((category) => (
          <li key={category.id}>
            <strong>{category.categoryName}</strong>
            {category.client && <span> (Client: {category.client.name})</span>}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ChildCategories;
